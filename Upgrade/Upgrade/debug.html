<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <title>Debug Desktop Functions</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 2rem; }
        .test { margin: 1rem 0; padding: 1rem; border: 1px solid #ccc; }
        .success { border-color: green; background: #e8f5e8; }
        .error { border-color: red; background: #ffe8e8; }
        button { padding: 0.5rem 1rem; margin: 0.25rem; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Debug Desktop Functions</h1>
    <div id="results"></div>
    
    <!-- Mock elements that the functions expect -->
    <div id="employee-grid" style="display:none;"></div>
    <table id="employees-table" style="display:none;">
        <tbody id="table-body"></tbody>
    </table>
    <div id="org-chart-view" style="display:none;">
        <div id="managers-level"></div>
        <div id="specialists-level"></div>
    </div>

    <!-- Load scripts -->
    <script src="js/desktop-enhancements.js?v=3"></script>
    
    <script>
        function addResult(message, isSuccess = true) {
            const div = document.createElement('div');
            div.className = isSuccess ? 'test success' : 'test error';
            div.innerHTML = `${isSuccess ? '✅' : '❌'} ${message}`;
            document.getElementById('results').appendChild(div);
            console.log(message);
        }
        
        // Mock data for testing
        window.employees = [
            {jmeno: "Test User", pozice: "ředitel", oddeleni: "Test", obrazek: "test.jpg", telefon: "123", email: "<EMAIL>"},
            {jmeno: "Test User 2", pozice: "specialista", oddeleni: "Test2", obrazek: "test2.jpg", telefon: "456", email: "<EMAIL>"}
        ];
        window.filteredEmployees = [...window.employees];
        
        // Test functions after load
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('=== DESKTOP FUNCTIONS DEBUG ===');
                
                // Test function existence
                const functions = ['switchView', 'sortTable', 'exportToExcel', 'exportToCSV', 'printDirectory'];
                functions.forEach(fname => {
                    if (typeof window[fname] === 'function') {
                        addResult(`Function ${fname} exists`);
                    } else {
                        addResult(`Function ${fname} MISSING`, false);
                    }
                });
                
                // Test switchView
                try {
                    if (typeof window.switchView === 'function') {
                        window.switchView('table');
                        addResult('switchView(table) executed');
                        
                        window.switchView('org-chart'); 
                        addResult('switchView(org-chart) executed');
                        
                        window.switchView('grid');
                        addResult('switchView(grid) executed');
                    } else {
                        addResult('switchView function not available', false);
                    }
                } catch (error) {
                    addResult(`switchView error: ${error.message}`, false);
                }
                
                console.log('=== DEBUG COMPLETE ===');
            }, 1000);
        });
    </script>
</body>
</html> 