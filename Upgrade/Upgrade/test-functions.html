<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test funkcí</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 2rem; 
            background: #f5f5f5;
        }
        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            margin: 0.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
        }
        .test-btn:hover {
            background: #2563eb;
        }
        .result {
            background: white;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            border-left: 4px solid #10b981;
        }
        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
        }
    </style>
</head>
<body>
    <h1>Test funkcí Desktop Enhancements</h1>
    
    <div>
        <button class="test-btn" onclick="testSwitchView()">Test switchView</button>
        <button class="test-btn" onclick="testFunctionExists()">Test function existence</button>
        <button class="test-btn" onclick="testConsoleLog()">Test console</button>
    </div>
    
    <div id="results"></div>
    
    <!-- Načteme skripty jako v main index.html -->
    <script src="js/navbar.js?v=9"></script>
    <script src="js/script.js?v=9"></script>
    <script src="js/desktop-enhancements.js?v=3"></script>
    
    <script>
        function addResult(message, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = isError ? 'result error' : 'result';
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
        }
        
        function testSwitchView() {
            try {
                if (typeof switchView === 'function') {
                    addResult('✅ switchView function exists');
                    console.log('Testing switchView...');
                    // switchView('table'); // Nebudeme volat, protože nemáme HTML elementy
                    addResult('✅ switchView function is callable');
                } else {
                    addResult('❌ switchView function not found', true);
                }
            } catch (error) {
                addResult(`❌ Error testing switchView: ${error.message}`, true);
            }
        }
        
        function testFunctionExists() {
            const functions = [
                'switchView',
                'sortTable', 
                'exportToExcel',
                'exportToCSV',
                'printDirectory',
                'applySavedSearch'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ ${funcName} exists and is a function`);
                } else {
                    addResult(`❌ ${funcName} not found or not a function`, true);
                }
            });
        }
        
        function testConsoleLog() {
            console.log('Test message from test page');
            addResult('✅ Check browser console for test message');
        }
        
        // Auto test po načtení
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 Page loaded, running auto tests...');
                testFunctionExists();
            }, 1000);
        });
    </script>
</body>
</html> 