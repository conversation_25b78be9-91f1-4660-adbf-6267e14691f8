<!DOCTYPE html>
<html lang="cs">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Adresář OTE">
    <meta name="author" content="<PERSON>">
    <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon">
    <title>Adresář OTE</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@200;300;400;600;700;800;900&display=swap"
        rel="stylesheet">
    <script src="https://kit.fontawesome.com/14940b9e28.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/directory.css">
    <link rel="stylesheet" href="css/map-integration.css">
    <link rel="stylesheet" href="css/desktop-enhancements.css?v=2">
    <style>
        /* Desktop-first layout optimizations */
        .desktop-layout {
            display: grid;
            grid-template-columns: 300px 1fr 280px;
            grid-template-rows: auto auto 1fr;
            height: 100vh;
            gap: 1rem;
            padding: 1rem;
            box-sizing: border-box;
        }

        .header-desktop {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            border-radius: 16px;
            padding: 1rem 2rem;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .stats-dashboard {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .stat-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: 900;
            display: block;
        }

        .sidebar-left {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .main-content {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            position: relative;
        }

        .sidebar-right {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .advanced-search-panel {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 2px solid #e5e7eb;
        }

        .search-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .search-row input, .search-row select {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .filter-chips {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .filter-chip {
            background: #e5e7eb;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-chip:hover, .filter-chip.active {
            background: #3b82f6;
            color: white;
        }

        /* Table and org chart styles moved to desktop-enhancements.css */

        .keyboard-shortcuts {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            font-size: 0.8rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 10000;
        }

        .keyboard-shortcuts.show {
            opacity: 1;
            visibility: visible;
        }

        /* Org chart styles moved to desktop-enhancements.css */

        .export-panel {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .export-panel h3 {
            margin: 0 0 1rem 0;
            color: #374151;
            font-size: 1rem;
        }

        .export-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            width: 100%;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .export-btn:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }

        .saved-searches {
            margin-top: 1rem;
        }

        .saved-searches label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #374151;
        }

        .saved-search-btn {
            display: block;
            width: 100%;
            padding: 0.5rem 0.75rem;
            margin-bottom: 0.25rem;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .saved-search-btn:hover {
            background: #f8fafc;
            border-color: #3b82f6;
        }

        /* Print styles */
        @media print {
            .desktop-layout {
                display: block !important;
            }
            
            .sidebar-left, .sidebar-right, .stats-dashboard, .view-toggles, .export-panel {
                display: none !important;
            }
            
            .main-content {
                box-shadow: none !important;
                padding: 0 !important;
            }
            
            .employees-table {
                font-size: 12px;
            }
            
            .employees-table th, .employees-table td {
                padding: 0.5rem 0.25rem;
            }
        }

        /* Dark mode support */
        .dark-mode .desktop-layout {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }

        .dark-mode .sidebar-left,
        .dark-mode .main-content,
        .dark-mode .sidebar-right {
            background: #334155;
            color: #e2e8f0;
        }

        .dark-mode .advanced-search-panel,
        .dark-mode .export-panel {
            background: #475569;
            border-color: #64748b;
        }

        .dark-mode .employees-table th {
            background: #475569;
            color: #e2e8f0;
        }

        .dark-mode .employees-table tr:hover {
            background: #475569;
        }

        /* ... existing code ... */
    </style>
</head>

<body>
    <div class="desktop-layout">
        <!-- Header -->
        <header class="header-desktop">
            <div class="logo">
                <img src="img/logo1.svg" alt="Logo OTE" class="logo1">
            </div>
            <h1 class="header-title" id="pageTitle">Adresář OTE</h1>
            <div class="view-toggle-header">
                <button id="employeeListBtn" class="view-toggle-btn active" data-view="employees" onclick="switchToEmployees()">
                    <i class="fas fa-users"></i>
                    <span>Adresář</span>
                </button>
                <button id="officeMapBtn" class="view-toggle-btn" data-view="map" onclick="switchToMap()">
                    <i class="fas fa-map-marked-alt"></i>
                    <span>Mapa</span>
                </button>
            </div>
            <div class="header-actions">
                <button class="theme-toggle-btn" id="themeToggle" title="Přepnout režim (Ctrl+T)">
                    <i class="fas fa-sun" id="themeIcon"></i>
                </button>
            </div>
        </header>

        <!-- Statistics Dashboard -->
        <div class="stats-dashboard" id="statsDashboard">
            <div class="stat-card">
                <h3>Celkem zaměstnanců</h3>
                <span class="number" id="totalEmployees">0</span>
            </div>
            <div class="stat-card">
                <h3>Počet oddělení</h3>
                <span class="number" id="totalDepartments">0</span>
            </div>
            <div class="stat-card">
                <h3>Vedoucí pozice</h3>
                <span class="number" id="totalManagers">0</span>
            </div>
            <div class="stat-card">
                <h3>Poslední aktualizace</h3>
                <span class="date" id="lastUpdated">Načítání...</span>
            </div>
        </div>

        <!-- Left Sidebar -->
        <aside class="sidebar-left">
            <!-- Advanced Search -->
            <div class="advanced-search-panel">
                <h3><i class="fas fa-search"></i> Pokročilé vyhledávání <kbd>Ctrl+F</kbd></h3>
                <div class="search-row">
                    <input type="text" id="mainSearchInput" placeholder="Jméno, pozice, dovednosti..." autocomplete="off">
                    <select id="departmentFilter">
                        <option value="">Všechna oddělení</option>
                    </select>
                    <select id="positionFilter">
                        <option value="">Všechny pozice</option>
                    </select>
                </div>
                
                <div class="filter-chips">
                    <button class="filter-chip" data-filter="managers">Pouze vedoucí</button>
                    <button class="filter-chip" data-filter="board">Představenstvo</button>
                    <button class="filter-chip" data-filter="has-phone">Má telefon</button>
                    <button class="filter-chip" data-filter="has-mobile">Má mobil</button>
                </div>

                <div class="saved-searches">
                    <label>Uložená vyhledávání:</label>
                    <button class="saved-search-btn" onclick="applySavedSearch('managers')">Manažeři</button>
                    <button class="saved-search-btn" onclick="applySavedSearch('it')">IT oddělení</button>
                    <button class="saved-search-btn" onclick="applySavedSearch('accounting')">Účetnictví</button>
                    <button class="saved-search-btn" onclick="applySavedSearch('board')">Představenstvo</button>
                </div>
            </div>

            <!-- Departments Panel -->
            <div class="departments-panel-directory" id="departmentsPanelDirectory">
                <div class="panel-header">
                    <h3><i class="fas fa-building"></i> Oddělení</h3>
                </div>
                <div class="departments-list" id="departmentsListDirectory"></div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- View Toggles -->
            <div class="view-toggles">
                <button class="view-toggle active" data-view="grid" onclick="switchView('grid')">
                    <i class="fas fa-th-large"></i> Karty
                </button>
                <button class="view-toggle" data-view="table" onclick="switchView('table')">
                    <i class="fas fa-table"></i> Tabulka
                </button>
                <button class="view-toggle" data-view="org-chart" onclick="switchView('org-chart')">
                    <i class="fas fa-sitemap"></i> Org. schéma
                </button>
            </div>

            <!-- Employee Grid View -->
            <div class="employee_listing" id="employee-grid">
                <!-- Existing employee cards -->
            </div>

            <!-- Table View -->
            <table class="employees-table" id="employees-table">
                <thead>
                    <tr>
                        <th onclick="sortTable(0)">Foto</th>
                        <th onclick="sortTable(1)">Jméno <i class="fas fa-sort"></i></th>
                        <th onclick="sortTable(2)">Pozice <i class="fas fa-sort"></i></th>
                        <th onclick="sortTable(3)">Oddělení <i class="fas fa-sort"></i></th>
                        <th onclick="sortTable(4)">Telefon <i class="fas fa-sort"></i></th>
                        <th onclick="sortTable(5)">Email <i class="fas fa-sort"></i></th>
                        <th>Akce</th>
                    </tr>
                </thead>
                <tbody id="table-body">
                    <!-- Table rows will be generated -->
                </tbody>
            </table>

            <!-- Org Chart View -->
            <div class="org-chart-view" id="org-chart-view">
                <div class="hierarchy-level level-1">
                    <div class="position-box ceo">
                        <h4>Představenstvo</h4>
                        <small>Vrcholové vedení</small>
                    </div>
                </div>
                <div class="hierarchy-level level-2" id="top-managers-level">
                    <!-- Top managers (Puchel, Chemišinec, Lobotková) -->
                </div>
                <div class="hierarchy-level level-3" id="departments-level">
                    <!-- Departments under each manager -->
                </div>
                <div class="hierarchy-level level-4" id="employees-level">
                    <!-- Employees in departments -->
                </div>
            </div>
        </main>

        <!-- Right Sidebar -->
        <aside class="sidebar-right">
            <!-- Export Panel -->
            <div class="export-panel">
                <h3><i class="fas fa-download"></i> Export & Tisk</h3>
                <button class="export-btn" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i>
                    Export do Excel <kbd>Ctrl+E</kbd>
                </button>
                <button class="export-btn" onclick="exportToCSV()">
                    <i class="fas fa-file-csv"></i>
                    Export do CSV
                </button>
                <button class="export-btn" onclick="printDirectory()">
                    <i class="fas fa-print"></i>
                    Tisknout seznam <kbd>Ctrl+P</kbd>
                </button>
                <button class="export-btn" onclick="exportVCards()">
                    <i class="fas fa-address-card"></i>
                    Export kontaktů
                </button>
            </div>

            <!-- Quick Stats -->
            <div class="export-panel">
                <h3><i class="fas fa-chart-bar"></i> Rychlé statistiky</h3>
                <div id="quickStats">
                    <div style="margin-bottom: 0.5rem;">
                        <strong>Aktuální výběr:</strong> <span id="currentSelection">Všichni</span>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <strong>Zobrazeno:</strong> <span id="displayedCount">0</span> zaměstnanců
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <strong>S telefonem:</strong> <span id="phoneCount">0</span>
                    </div>
                    <div>
                        <strong>S mobilem:</strong> <span id="mobileCount">0</span>
                    </div>
                </div>
            </div>

            <!-- Map Integration (when map view is active) -->
            <div class="departments-panel" id="departmentsPanel" style="display: none;">
                <div class="panel-header">
                    <h3><i class="fas fa-building"></i> Oddělení</h3>
                </div>
                <div class="departments-list" id="departmentsList"></div>
            </div>
        </aside>
    </div>

    <!-- Map Section (hidden by default) -->
    <section class="office_map_section" id="mapSection" style="display: none;">
        <!-- Existing map content -->
        <div class="main-flex main-flex-custom">
            <div class="employee-bar">
                <div class="unified-search-container" id="mapSearchContainer">
                    <div class="search-row-inline">
                        <div class="section-indicator-inline">
                            <i class="fas fa-building"></i>
                            <span id="mapCurrentSection">Všichni zaměstnanci</span>
                        </div>
                        <div class="search-window-inline">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="mapSearchInput" placeholder="Vyhledat zaměstnance..." autocomplete="off" class="search-input">
                                <button type="button" class="clear-btn" id="mapSearchClearBtn" style="display: none;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <ul class="employee-list-horizontal" id="mapEmployeeList"></ul>
            </div>
            <div class="mapa-wrapper mapa-wrapper-custom">
                <div class="office-map-container" id="mapContainer">
                    <img id="office-map-img" src="img/Greenline.png" alt="Plán kanceláře">
                </div>
            </div>
        </div>
        <div id="map-error" class="error-message">Obrázek plánu kanceláře se nepodařilo načíst.</div>
    </section>

    <!-- Keyboard Shortcuts Help -->
    <div class="keyboard-shortcuts" id="keyboardShortcuts">
        <h4>Klávesové zkratky:</h4>
        <div><kbd>Ctrl+F</kbd> - Zaměřit vyhledávání</div>
        <div><kbd>Ctrl+M</kbd> - Přepnout na mapu</div>
        <div><kbd>Ctrl+D</kbd> - Přepnout na adresář</div>
        <div><kbd>Ctrl+E</kbd> - Export do Excel</div>
        <div><kbd>Ctrl+P</kbd> - Tisknout</div>
        <div><kbd>Ctrl+T</kbd> - Tmavý režim</div>
        <div><kbd>F1</kbd> - Zobrazit/skrýt zkratky</div>
        <div><kbd>Esc</kbd> - Zavřít modál</div>
    </div>

    <!-- Employee Modal (existing) -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-header-section">
                <img id="modalImage" src="" alt="Employee Image">
                <h2 id="modalName"></h2>
            </div>
            <div class="modal-info-cards">
                <div class="info-card" id="modalPosition">
                    <i class="fas fa-briefcase"></i>
                    <span>Pracovní pozice: Specialista</span>
                </div>
                <div class="info-card" id="modalDepartment">
                    <i class="fas fa-building"></i>
                    <span>Oddělení: Smluvní vztahy a povolenky</span>
                </div>
            </div>
            <div id="modalOffice"></div>
            <div class="modal-contact-section">
                <div class="contact-grid">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span id="modalPhone">Telefon: +420 234 686 370</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-mobile-alt"></i>
                        <span id="modalMobile">Mobil: +420 603 568 443</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span id="modalEmail">Email: <EMAIL></span>
                    </div>
                </div>
                <div id="modalTeams"></div>
            </div>
        </div>
    </div>

    <footer id="Footer">
        <p>Poslední aktualizace: <span id="lastUpdated"></span></p>
    </footer>

    <button id="backToTopBtn" title="Zpět nahoru">
        <i class="fas fa-chevron-up"></i>
        <span>Nahoru</span>
    </button>

    <script src="js/navbar.js?v=9"></script>
    <script src="js/script.js?v=9"></script>
    <script src="js/back-to-top.js?v=9"></script>
    <script src="js/update-date.js?v=9"></script>
    <script src="js/map-integration.js?v=9"></script>
    <script src="js/desktop-enhancements.js?v=5"></script>

    <script>
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });
        
        // Debug script - remove in production
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('=== DEBUG INFO ===');
                console.log('switchView function:', typeof window.switchView);
                console.log('employees data length:', typeof employees !== 'undefined' ? employees.length : 'undefined');
                console.log('filteredEmployees data length:', typeof filteredEmployees !== 'undefined' ? filteredEmployees.length : 'undefined');
                
                const elementChecks = {
                    'employee-grid': document.getElementById('employee-grid'),
                    'employees-table': document.getElementById('employees-table'),
                    'org-chart-view': document.getElementById('org-chart-view'),
                    'top-managers-level': document.getElementById('top-managers-level'),
                    'departments-level': document.getElementById('departments-level'),
                    'employees-level': document.getElementById('employees-level'),
                    'table-body': document.getElementById('table-body')
                };
                
                console.log('Required elements:', elementChecks);
                
                Object.entries(elementChecks).forEach(([name, element]) => {
                    if (!element) {
                        console.error(`❌ Missing element: ${name}`);
                    } else {
                        console.log(`✅ Element found: ${name}`);
                    }
                });
                
                console.log('==================');
            }, 2000);
        });
    </script>
</body>

</html>
