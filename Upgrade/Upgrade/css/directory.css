html {
    scroll-behavior: smooth;
}

.employee_directory {
    width: 90%;
    margin: 0 auto;
    margin-top: 2rem;
    margin-bottom: 150px;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

.employee_directory h1 {
    text-align: center;
    font-size: 2em;
    color: #00808f;
    margin-bottom: 40px;
}

form.search_form {
    position: relative;
    margin-bottom: 20px;
}

form.search_form input {
    width: 100%;
    padding: 10px 10px;
    font-size: 1em;
    border: solid 1px #C6C6C6;
    border-radius: 5px;
    margin: 0 auto;
    transition: .2s ease-in;
}

form.search_form input:focus {
    border-color: #00808f;
    outline: none;
}

form.search_form i {
    position: absolute;
    top: 15px;
    right: 10px;
    color: #C6C6C6;
}

.employee_listing .employee {
    padding: 40px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    margin-right: auto;
    margin-left: auto;
    border-radius: 20px;
    text-align: center;
    width: 100%;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 128, 143, 0.1);
}

.employee_listing .employee img {
    max-width: 180px;
    max-height: 180px;
    border-radius: 180px;
    overflow: auto;
    object-fit: cover;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: brightness(1) contrast(1) saturate(1);
    border: 3px solid transparent;
    position: relative;
}

.employee_listing .employee:hover img {
    transform: scale(1.05);
    filter: brightness(1.1) contrast(1.05) saturate(1.1);
    box-shadow:
        0 8px 25px rgba(0, 128, 143, 0.2),
        0 0 0 2px rgba(0, 128, 143, 0.1),
        inset 0 0 0 2px rgba(255, 255, 255, 0.6);
    border-color: rgba(0, 128, 143, 0.3);
}

.employee_listing .employee h2 {
    font-size: 18px;
    font-weight: 900;
    color: #00808f;
    margin-bottom: 8px;
    line-height: 1.2;
}

.employee_listing .employee .employee_name {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.employee_listing .employee .first-name {
    font-size: 18px;
    font-weight: 900;
    color: #00808f;
    margin: 0;
}

.employee_listing .employee .last-name {
    font-size: 16px;
    font-weight: 700;
    color: #005a66;
    margin: 0;
}

.employee_listing .employee .position {
    font-size: 14px;
    color: #666;
    margin: 5px 0;
    font-weight: 600;
}

.employee_listing .employee .job_type {
    font-weight: 900;
    padding: 12px 16px;
    background: linear-gradient(135deg, #00808f 0%, #00add0 100%);
    border-radius: 12px;
    color: #fff;
    font-size: 14px;
    margin-top: 15px;
    box-shadow: 0 3px 10px rgba(0, 128, 143, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.employee_listing .employee:hover {
    background: linear-gradient(135deg, #00add0 0%, #00808f 100%);
    transform: scale(1.02);
    box-shadow:
        0 8px 25px rgba(0, 128, 143, 0.2),
        0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 128, 143, 0.3);
}

.employee_listing .employee:hover h2,
.employee_listing .employee:hover .first-name,
.employee_listing .employee:hover .last-name,
.employee_listing .employee:hover .position {
    color: #fff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.employee_listing .employee:hover .job_type {
    background-color: #333;
    border: 1px solid #fff;
}

.employee_listing .employee::before {
    content: attr(data-number);
    position: absolute;
    top: 0;
    left: 0;
    background-color: #00808f;
    color: white;
    font-size: 12px;
    font-weight: bold;
    padding: 4px 8px;
    border-bottom-right-radius: 10px;
    min-width: 24px;
    text-align: center;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.employee_listing .employee:hover::before {
    background-color: #005a66;
    box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.2);
}

.employee_listing .employee .click-info {
    position: absolute;
    bottom: -60px;
    left: 0;
    right: 0;
    background-color: rgba(0, 128, 143, 0.95);
    color: white;
    padding: 12px;
    text-align: center;
    transition: bottom 0.3s ease-in-out;
    font-size: 14px;
    backdrop-filter: blur(5px);
}

.employee_listing .employee:hover .click-info {
    bottom: 0;
}



.employee_listing .employee .click-info i {
    margin-right: 5px;
    animation: pointUp 1s infinite alternate;
    font-size: 18px;
}

@keyframes pointUp {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-5px);
    }
}


.employee img {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.employee img:hover {
    transform: scale(1.1) !important;
}

@media (min-width: 1200px) {
    .employee_listing {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        max-width: 1900px;
        margin-left: auto;
        margin-right: auto;
    }

    .employee_listing .employee {
        width: 350px;
        height: 420px;
        cursor: pointer;
    }

    form.search_form {
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 40px;
    }
}

@media (min-width: 1366px) {
    .employee_listing .employee {
        width: 300px;
    }
}

@media (max-width: 768px) {
    .employee_directory {
        width: 95%;
        margin-top: 120px;
    }

    .department-filters {
        top: 80px;
        padding: 10px;
        margin-bottom: 20px;
    }

    .employee_listing .employee {
        padding: 20px;
        margin-bottom: 15px;
    }

    .employee_listing .employee img {
        max-width: 120px;
        max-height: 120px;
    }

    .employee_listing .employee .first-name {
        font-size: 16px;
    }

    .employee_listing .employee .last-name {
        font-size: 14px;
    }

    .employee_listing .employee .position {
        font-size: 12px;
    }

    .employee_listing .employee .job_type {
        font-size: 12px;
        padding: 8px;
    }
}

.department-filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.filter-btn {
    background-color: #f0f0f0;
    border: none;
    border-radius: 20px;
    color: #333;
    cursor: pointer;
    font-size: 14px;
    margin: 5px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background-color: #00add0;
    color: white;
}

.filter-btn.active {
    background-color: #00808f;
    color: white;
}



.filter-btn i {
    margin-right: 5px;
}

.filter-btn .count {
    font-size: 12px;
    background: rgba(255, 255, 255, 0.3);
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
    font-weight: bold;
}

.filter-btn.active .count {
    background: rgba(255, 255, 255, 0.2);
}

.filter-btn:hover .count {
    background: rgba(255, 255, 255, 0.3);
}

@media (max-width: 768px) {
    .department-filters {
        flex-direction: column;
    }

    .filter-btn {
        width: 100%;
        margin: 5px 0;
    }

    .employee_listing .employee {
        width: 100%;
    }
}

.filter-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.filter-header i {
    font-size: 24px;
    color: #00808f;
    margin-right: 10px;
}

.filter-header h2 {
    font-size: 20px;
    color: #00808f;
    margin: 0;
}

.click-info {
    display: block;
}


.search_form input[type="text"] {
    width: 100%;
    padding: 10px 16px;
    border-radius: 8px;
    border: 1px solid #e0e7ef;
    background: #f8f9fa;
    font-size: 1rem;
    margin-bottom: 18px;
    outline: none;
    transition: border 0.2s;
}

.search_form input[type="text"]:focus {
    border: 1.5px solid #2563eb;
}

.department-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 18px;
}

.filter-btn {
    background: #e0e7ef;
    color: #2563eb;
    border: none;
    border-radius: 8px;
    padding: 7px 16px;
    font-size: 0.98rem;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}

.filter-btn.active, .filter-btn:hover {
    background: #2563eb;
    color: #fff;
}

.filter-btn i {
    margin-right: 5px;
}

@media (max-width: 768px) {
    .department-filters {
        flex-direction: column;
    }
    
    .filter-btn {
        width: 100%;
        margin: 5px 0;
    }

    .employee_listing .employee {
        width: 100%;
    }
}

.filter-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.employee-count-container, .map-link-container {
    display: flex;
    align-items: center;
    gap: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px 25px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.employee-count-container:hover, .map-link-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.employee-count-container i, .map-link-container i {
    font-size: 24px;
    color: #00808f;
}

.employee-count-container h2, .map-link-container h2 {
    font-size: 18px;
    color: #00808f;
    margin: 0;
    font-weight: 700;
}

.map-link-container {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    cursor: pointer;
    text-decoration: none;
}

.map-link-container i, .map-link-container h2 {
    color: white;
}

.map-link-container:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    box-shadow: 0 4px 20px rgba(37, 99, 235, 0.3);
}


.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #00808f;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #00808f;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}


.no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #666;
    text-align: center;
}

.no-results i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}

.no-results p {
    margin: 5px 0;
    font-size: 18px;
}

.no-results .hint {
    font-size: 14px;
    color: #999;
}

.department-filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 10px;
}

@media (min-width: 1200px) {
    .employee_listing {
        display: flex; 
        flex-wrap: wrap;
        justify-content: space-between;
        max-width: 1900px;
        margin-left: auto;
        margin-right: auto;
    }

    .employee_listing .employee {
        width: 350px;
        height: 420px;
        cursor: pointer;
    }

    form.search_form {
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 40px;
    }
}

@media (min-width: 1366px) {
    .employee_listing .employee {
        width: 300px;
    }
}

@media (max-width: 768px) {
    .employee_listing .employee {
        padding: 20px;
    }

    .employee_listing .employee img {
        max-width: 120px;
        max-height: 120px;
    }
}


.dark-mode .employee_directory {
    color: #e2e8f0;
}

.dark-mode .employee_listing .employee {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid #475569;
}

.dark-mode .employee_listing .employee:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.3);
    border-color: #3b82f6;
}

.dark-mode .employee_listing .employee h2 {
    color: #e2e8f0;
}

.dark-mode .employee_listing .employee:hover h2 {
    color: white;
}

.dark-mode .employee_listing .employee .job_type {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border-color: #3b82f6;
}

.dark-mode .employee_listing .employee:hover .job_type {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: white;
}

.dark-mode .employee_listing .employee .department {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border-color: #10b981;
}

.dark-mode .employee_listing .employee:hover .department {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: white;
}

.dark-mode .filter-btn {
    background: #334155;
    color: #e2e8f0;
    border-color: #475569;
}

.dark-mode .filter-btn:hover {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.dark-mode .filter-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}