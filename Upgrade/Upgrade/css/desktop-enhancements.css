/* Desktop Enhancements CSS */
/* Version 1.0 - Business-focused desktop improvements */

/* Additional desktop-specific grid styles */
.employee_listing {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 0;
}

/* Enhanced employee cards */
.employee {
    background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.employee::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.employee:hover {
    transform: translateY(-4px) scale(1.02);
    border-color: #3b82f6;
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);
}

.employee:hover::before {
    opacity: 1;
}

/* Professional employee photo styling */
.employee img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #ffffff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.employee:hover img {
    transform: scale(1.05);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.2);
}

/* Enhanced typography */
.employee .first-name,
.employee .last-name {
    font-weight: 700;
    color: #1f2937;
    font-size: 1.1rem;
    line-height: 1.3;
    margin-bottom: 0.5rem;
}

.employee .position {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    padding: 0.25rem 0.75rem;
    background: #f3f4f6;
    border-radius: 20px;
    display: inline-block;
}

.employee .job_type {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    border: none;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.employee:hover .job_type {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Department panel enhancements */
.departments-panel-directory,
.departments-panel {
    background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.departments-panel-directory .panel-header,
.departments-panel .panel-header {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    padding: 1.5rem;
    border-bottom: none;
}

.departments-panel-directory .panel-header h3,
.departments-panel .panel-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.departments-list {
    padding: 1.5rem;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.departments-list::-webkit-scrollbar {
    width: 6px;
}

.departments-list::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.departments-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.departments-list::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.department-item {
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem 1.25rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.department-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.department-item:hover {
    background: #f8fafc;
    border-color: #3b82f6;
    transform: translateX(4px);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.12);
}

.department-item:hover::before {
    opacity: 1;
}

.department-item.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border-color: #1d4ed8;
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.25);
    transform: translateX(4px);
}

.department-item.active::before {
    opacity: 0;
}

.department-item.all-employees {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    border-color: #374151;
}

.department-item.all-employees:hover,
.department-item.all-employees.active {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    border-color: #1f2937;
}

.department-name {
    font-weight: 700;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.department-count {
    font-size: 0.85rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Enhanced search and filter styling */
.unified-search-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 255, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(59, 130, 246, 0.15);
    border-radius: 16px;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.08);
    transition: all 0.3s ease;
}

.unified-search-container:hover {
    box-shadow: 0 12px 48px rgba(59, 130, 246, 0.12);
    border-color: rgba(59, 130, 246, 0.25);
}

/* Professional button styling */
.view-toggle-btn {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.view-toggle-btn:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.view-toggle-btn.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border-color: #1d4ed8;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.view-toggle-btn i {
    font-size: 1rem;
    opacity: 0.9;
}

/* Dark mode enhancements */
.dark-mode .desktop-layout {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.dark-mode .header-desktop {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border: 2px solid #475569;
}

.dark-mode .sidebar-left,
.dark-mode .main-content,
.dark-mode .sidebar-right {
    background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    color: #e2e8f0;
    border: 2px solid #475569;
}

.dark-mode .stat-card {
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
    border: 2px solid #3b82f6;
    box-shadow: 0 4px 20px rgba(30, 64, 175, 0.3);
}

.dark-mode .departments-panel-directory,
.dark-mode .departments-panel {
    background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    border-color: #475569;
}

.dark-mode .departments-panel-directory .panel-header,
.dark-mode .departments-panel .panel-header {
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
}

.dark-mode .department-item {
    background: #475569;
    border-color: #64748b;
    color: #e2e8f0;
}

.dark-mode .department-item:hover {
    background: #64748b;
    border-color: #3b82f6;
}

.dark-mode .department-item.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: #ffffff;
    border-color: #1d4ed8;
}

.dark-mode .employee {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
    border-color: #64748b;
    color: #e2e8f0;
}

.dark-mode .employee:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-color: #60a5fa;
    color: #ffffff;
}

.dark-mode .employee .first-name,
.dark-mode .employee .last-name {
    color: #e2e8f0;
}

.dark-mode .employee:hover .first-name,
.dark-mode .employee:hover .last-name {
    color: #ffffff;
}

.dark-mode .employee .position {
    background: #64748b;
    color: #e2e8f0;
}

.dark-mode .unified-search-container {
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
    border-color: rgba(59, 130, 246, 0.3);
    backdrop-filter: blur(20px);
}

.dark-mode .view-toggle-btn {
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
    border-color: #64748b;
    color: #e2e8f0;
}

.dark-mode .view-toggle-btn:hover {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border-color: #3b82f6;
    color: #60a5fa;
}

.dark-mode .view-toggle-btn.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: #ffffff;
    border-color: #1d4ed8;
}

/* Responsive adjustments for desktop focus */
@media (min-width: 1200px) {
    .desktop-layout {
        grid-template-columns: 320px 1fr 300px;
        padding: 1.5rem;
        gap: 1.5rem;
    }
    
    .employee_listing {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 2rem;
    }
    
    .employee {
        padding: 2rem;
    }
    
    .employee img {
        width: 100px;
        height: 100px;
    }
}

@media (min-width: 1400px) {
    .desktop-layout {
        grid-template-columns: 350px 1fr 320px;
        padding: 2rem;
        gap: 2rem;
    }
    
    .employee_listing {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2.5rem;
    }
}

/* Professional hierarchy badges */
.hierarchy-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 700;
    z-index: 5;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 0.4rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hierarchy-badge.predstavenstvo {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.hierarchy-badge.vedouci {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.hierarchy-badge i {
    font-size: 0.7rem;
    opacity: 0.9;
}

.dark-mode .hierarchy-badge {
    border-color: rgba(255, 255, 255, 0.2);
}

/* Enhanced modal styling */
.modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: 24px;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(25px);
    padding: 2rem;
    max-width: 500px;
    width: 90%;
}

.dark-mode .modal-content {
    background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    border-color: rgba(59, 130, 246, 0.3);
    color: #e2e8f0;
}

/* Animation enhancements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.employee {
    animation: fadeInUp 0.6s ease-out forwards;
}

.department-item {
    animation: slideInLeft 0.4s ease-out forwards;
}

.employee:nth-child(even) {
    animation-delay: 0.1s;
}

.employee:nth-child(3n) {
    animation-delay: 0.2s;
}

.department-item:nth-child(n) {
    animation-delay: calc(var(--i, 0) * 0.1s);
}

/* Table and Org Chart Views */
.view-toggles {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #e5e7eb;
}

.view-toggle {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    background: #f8fafc;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.view-toggle.active {
    background: #3b82f6;
    color: white;
}

.employees-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    display: none;
}

.employees-table.active {
    display: table;
}

.employees-table th {
    background: #f8fafc;
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 700;
    border-bottom: 2px solid #e5e7eb;
    cursor: pointer;
    user-select: none;
}

.employees-table th:hover {
    background: #e5e7eb;
}

.employees-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: middle;
}

.employees-table tr:hover {
    background: #f8fafc;
}

.table-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: #6b7280;
}

.action-btn.secondary:hover {
    background: #4b5563;
}

.org-chart-view {
    display: none;
    padding: 2rem;
    overflow-x: auto;
}

.org-chart-view.active {
    display: block;
}

.hierarchy-level {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.position-box {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    text-align: center;
    min-width: 150px;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.position-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.position-box.ceo {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.position-box.manager {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.position-box.specialist {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.position-box h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 700;
}

.position-box small {
    font-size: 0.8rem;
    opacity: 0.9;
}

.position-box::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 15px;
    background: #e5e7eb;
}

.hierarchy-level:last-child .position-box::after {
    display: none;
}

.position-box.department {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    font-size: 0.9rem;
    min-width: 180px;
}

.position-box.employee {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
    font-size: 0.85rem;
    min-width: 140px;
    padding: 0.75rem 1rem;
}

.hierarchy-level.level-4 {
    flex-wrap: wrap;
    max-width: 100%;
    justify-content: flex-start;
}

.hierarchy-level.level-4 .position-box {
    margin: 0.25rem;
}

/* Dark mode for tables and org chart */
.dark-mode .employees-table th {
    background: #475569;
    color: #e2e8f0;
    border-bottom-color: #64748b;
}

.dark-mode .employees-table td {
    border-bottom-color: #64748b;
}

.dark-mode .employees-table tr:hover {
    background: #475569;
}

.dark-mode .view-toggle {
    background: #475569;
    color: #e2e8f0;
}

.dark-mode .view-toggle.active {
    background: #3b82f6;
    color: white;
}

.dark-mode .view-toggles {
    border-color: #64748b;
}

/* Print optimizations */
@media print {
    .desktop-layout {
        display: block !important;
        background: white !important;
    }
    
    .sidebar-left,
    .sidebar-right,
    .stats-dashboard,
    .view-toggles {
        display: none !important;
    }
    
    .main-content {
        background: white !important;
        box-shadow: none !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .employee_listing {
        display: block !important;
    }
    
    .employee {
        background: white !important;
        border: 1px solid #000 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
        margin-bottom: 1rem;
        padding: 1rem !important;
    }
    
    .employee img {
        width: 60px !important;
        height: 60px !important;
        border: 1px solid #000 !important;
    }
    
    .hierarchy-badge {
        background: #000 !important;
        color: white !important;
        box-shadow: none !important;
    }
} 