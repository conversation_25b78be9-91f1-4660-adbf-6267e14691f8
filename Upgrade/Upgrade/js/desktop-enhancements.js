// Desktop Enhancements for OTE Directory
// Version 1.0 - Desktop-first improvements

let desktopCurrentView = 'grid';
let sortDirection = 'asc';
let sortColumn = 1;
let filteredData = [];
let allEmployeesData = [];

// Initialize desktop enhancements
document.addEventListener('DOMContentLoaded', function() {
    console.log('Desktop enhancements loading...');
    initializeKeyboardShortcuts();
    initializeAdvancedSearch();
    initializeExportFunctions();
    initializeViewSwitching();
    
    // Wait for employee data to load from the main script
    const checkData = setInterval(() => {
        if (typeof employees !== 'undefined' && employees.length > 0) {
            clearInterval(checkData);
            allEmployeesData = [...employees];
            // Use existing filteredEmployees if available, otherwise use all employees
            if (typeof filteredEmployees !== 'undefined' && filteredEmployees.length > 0) {
                filteredData = [...filteredEmployees];
            } else {
                filteredData = [...employees];
            }
            
            initializeDashboard();
            updateDashboardStats();
            populateFilterDropdowns();
            
            // Set initial view to grid and update it
            desktopCurrentView = 'grid';
            updateCurrentView(); // Update the current view with loaded data
            console.log('Desktop enhancements initialized with', employees.length, 'employees');
            console.log('Current filteredData length:', filteredData.length);
        }
    }, 100);
});

// Keyboard Shortcuts
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+F - Focus search
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            document.getElementById('mainSearchInput').focus();
        }
        
        // Ctrl+M - Switch to map
        if (e.ctrlKey && e.key === 'm') {
            e.preventDefault();
            switchToMap();
        }
        
        // Ctrl+D - Switch to directory
        if (e.ctrlKey && e.key === 'd') {
            e.preventDefault();
            switchToEmployees();
        }
        
        // Ctrl+E - Export to Excel
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportToExcel();
        }
        
        // Ctrl+P - Print
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printDirectory();
        }
        
        // Ctrl+T - Toggle theme
        if (e.ctrlKey && e.key === 't') {
            e.preventDefault();
            document.getElementById('themeToggle').click();
        }
        
        // F1 - Show/hide keyboard shortcuts
        if (e.key === 'F1') {
            e.preventDefault();
            toggleKeyboardShortcuts();
        }
        
        // Escape - Close modals
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });
}

function toggleKeyboardShortcuts() {
    const shortcuts = document.getElementById('keyboardShortcuts');
    shortcuts.classList.toggle('show');
    
    if (shortcuts.classList.contains('show')) {
        setTimeout(() => shortcuts.classList.remove('show'), 5000);
    }
}

function closeAllModals() {
    const modal = document.getElementById('employeeModal');
    if (modal.style.display === 'flex') {
        modal.style.display = 'none';
    }
    
    const shortcuts = document.getElementById('keyboardShortcuts');
    shortcuts.classList.remove('show');
}

// Advanced Search
function initializeAdvancedSearch() {
    const mainSearch = document.getElementById('mainSearchInput');
    const departmentFilter = document.getElementById('departmentFilter');
    const positionFilter = document.getElementById('positionFilter');
    const filterChips = document.querySelectorAll('.filter-chip');
    
    // Main search input
    mainSearch.addEventListener('input', debounce(performAdvancedSearch, 300));
    
    // Filter dropdowns
    departmentFilter.addEventListener('change', performAdvancedSearch);
    positionFilter.addEventListener('change', performAdvancedSearch);
    
    // Filter chips
    filterChips.forEach(chip => {
        chip.addEventListener('click', function() {
            this.classList.toggle('active');
            performAdvancedSearch();
        });
    });
}

function populateFilterDropdowns() {
    if (!allEmployeesData.length) return;
    
    const departments = [...new Set(allEmployeesData.map(emp => emp.oddeleni))].sort();
    const positions = [...new Set(allEmployeesData.map(emp => emp.pozice))].sort();
    
    const departmentFilter = document.getElementById('departmentFilter');
    const positionFilter = document.getElementById('positionFilter');
    
    // Clear existing options (except first one)
    departmentFilter.innerHTML = '<option value="">Všechna oddělení</option>';
    positionFilter.innerHTML = '<option value="">Všechny pozice</option>';
    
    // Add departments
    departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        departmentFilter.appendChild(option);
    });
    
    // Add positions
    positions.forEach(pos => {
        const option = document.createElement('option');
        option.value = pos;
        option.textContent = pos;
        positionFilter.appendChild(option);
    });
}

function performAdvancedSearch() {
    const searchText = document.getElementById('mainSearchInput').value.toLowerCase();
    const selectedDepartment = document.getElementById('departmentFilter').value;
    const selectedPosition = document.getElementById('positionFilter').value;
    const activeChips = Array.from(document.querySelectorAll('.filter-chip.active')).map(chip => chip.dataset.filter);
    
    filteredData = allEmployeesData.filter(employee => {
        // Text search
        const matchesText = !searchText || 
            employee.jmeno.toLowerCase().includes(searchText) ||
            employee.pozice.toLowerCase().includes(searchText) ||
            employee.oddeleni.toLowerCase().includes(searchText) ||
            employee.email.toLowerCase().includes(searchText);
        
        // Department filter
        const matchesDepartment = !selectedDepartment || employee.oddeleni === selectedDepartment;
        
        // Position filter
        const matchesPosition = !selectedPosition || employee.pozice === selectedPosition;
        
        // Chip filters
        let matchesChips = true;
        activeChips.forEach(chip => {
            switch(chip) {
                case 'managers':
                    matchesChips = matchesChips && (
                        employee.pozice.toLowerCase().includes('vedoucí') ||
                        employee.pozice.toLowerCase().includes('ředitel') ||
                        employee.pozice.toLowerCase().includes('předseda')
                    );
                    break;
                case 'board':
                    matchesChips = matchesChips && employee.oddeleni.includes('Představenstvo');
                    break;
                case 'has-phone':
                    matchesChips = matchesChips && employee.telefon && employee.telefon.trim() !== '';
                    break;
                case 'has-mobile':
                    matchesChips = matchesChips && employee.mobil && employee.mobil.trim() !== '';
                    break;
            }
        });
        
        return matchesText && matchesDepartment && matchesPosition && matchesChips;
    });
    
    // Update global filteredEmployees if it exists (for compatibility)
    if (typeof filteredEmployees !== 'undefined') {
        filteredEmployees.length = 0;
        filteredEmployees.push(...filteredData);
    }
    
    updateCurrentView();
    updateQuickStats();
}

// Saved searches
function applySavedSearch(type) {
    // Clear current filters
    document.getElementById('mainSearchInput').value = '';
    document.getElementById('departmentFilter').value = '';
    document.getElementById('positionFilter').value = '';
    document.querySelectorAll('.filter-chip').forEach(chip => chip.classList.remove('active'));
    
    switch(type) {
        case 'managers':
            document.querySelector('[data-filter="managers"]').classList.add('active');
            break;
        case 'it':
            document.getElementById('departmentFilter').value = 'ICT služby';
            break;
        case 'accounting':
            document.getElementById('departmentFilter').value = 'Účetnictví a daně';
            break;
        case 'board':
            document.querySelector('[data-filter="board"]').classList.add('active');
            break;
    }
    
    performAdvancedSearch();
}

// View Switching
function initializeViewSwitching() {
    // View toggle buttons are handled by onclick attributes in HTML
}

function switchView(view) {
    console.log('Switching to view:', view);
    desktopCurrentView = view;
    
    // Update toggle buttons
    document.querySelectorAll('.view-toggle').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const targetButton = document.querySelector(`[data-view="${view}"]`);
    if (targetButton) {
        targetButton.classList.add('active');
    } else {
        console.error('Target button not found for view:', view);
    }
    
    // Show/hide appropriate views
    const employeeGrid = document.getElementById('employee-grid');
    const employeesTable = document.getElementById('employees-table');
    const orgChartView = document.getElementById('org-chart-view');
    
    if (!employeeGrid || !employeesTable || !orgChartView) {
        console.error('Missing view elements:', {
            employeeGrid: !!employeeGrid,
            employeesTable: !!employeesTable,
            orgChartView: !!orgChartView
        });
        return;
    }
    
    // Hide all views first
    employeeGrid.style.display = 'none';
    employeesTable.classList.remove('active');
    orgChartView.classList.remove('active');
    
    // Show the selected view
    switch(view) {
        case 'grid':
            employeeGrid.style.display = 'grid';
            break;
        case 'table':
            employeesTable.classList.add('active');
            break;
        case 'org-chart':
            orgChartView.classList.add('active');
            break;
        default:
            console.error('Unknown view:', view);
            employeeGrid.style.display = 'grid'; // fallback to grid
    }
    
    updateCurrentView();
}

function updateCurrentView() {
    console.log('Updating current view:', desktopCurrentView, 'with', filteredData.length, 'employees');
    
    switch(desktopCurrentView) {
        case 'grid':
            updateGridView();
            break;
        case 'table':
            updateTableView();
            break;
        case 'org-chart':
            updateOrgChartView();
            break;
        default:
            console.error('Unknown current view:', desktopCurrentView);
            updateGridView(); // fallback
    }
}

// Grid View (existing functionality)
function updateGridView() {
    if (typeof generateEmployeeHTML === 'function' && typeof filteredEmployees !== 'undefined') {
        // Update existing filteredEmployees array with our filtered data
        filteredEmployees.length = 0;
        filteredEmployees.push(...filteredData);
        generateEmployeeHTML();
    } else {
        // Fallback if existing functions not available
        generateSimpleGridView();
    }
}

function generateSimpleGridView() {
    const employeeGrid = document.getElementById('employee-grid');
    if (!employeeGrid) return;
    
    employeeGrid.innerHTML = '';
    
    filteredData.forEach((employee, index) => {
        const employeeDiv = document.createElement('div');
        employeeDiv.className = 'employee';
        
        const nameParts = employee.jmeno.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';
        
        employeeDiv.innerHTML = `
            <img src="${employee.obrazek}" alt="${employee.jmeno}" onerror="this.src='img/no-person-photo.png'">
            <div class="employee_name">
                <div class="first-name">${firstName}</div>
                <div class="last-name">${lastName}</div>
            </div>
            <p class="position">${employee.pozice}</p>
            <p class="job_type">${employee.oddeleni}</p>
            <div class="click-info">
                <i class="fas fa-hand-pointer"></i>
                <span>Více informací zobrazíte kliknutím</span>
            </div>
        `;
        
        employeeDiv.addEventListener('click', () => {
            if (typeof openModal === 'function') {
                openModal(employee);
            } else {
                openEmployeeModal(employee.jmeno);
            }
        });
        
        employeeGrid.appendChild(employeeDiv);
    });
}

// Table View
function updateTableView() {
    console.log('Updating table view with', filteredData.length, 'employees');
    
    const tableBody = document.getElementById('table-body');
    if (!tableBody) {
        console.error('Table body element not found');
        return;
    }
    
    tableBody.innerHTML = '';
    
    if (filteredData.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="7" style="text-align: center; color: #6b7280;">Žádní zaměstnanci k zobrazení</td>';
        tableBody.appendChild(row);
        return;
    }
    
    filteredData.forEach(employee => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><img src="${employee.obrazek}" alt="${employee.jmeno}" class="table-photo" onerror="this.src='img/no-person-photo.png'"></td>
            <td><strong>${employee.jmeno}</strong></td>
            <td>${employee.pozice}</td>
            <td>${employee.oddeleni}</td>
            <td>${employee.telefon || '-'}</td>
            <td>${employee.email || '-'}</td>
            <td class="table-actions">
                <button class="action-btn" onclick="openEmployeeModal('${employee.jmeno}')">
                    <i class="fas fa-eye"></i> Detail
                </button>
                <button class="action-btn secondary" onclick="callEmployee('${employee.telefon}')">
                    <i class="fas fa-phone"></i> Volat
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
    
    // Update quick stats after table is populated
    updateQuickStats();
}

// Table Sorting
function sortTable(columnIndex) {
    if (sortColumn === columnIndex) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = columnIndex;
        sortDirection = 'asc';
    }
    
    filteredData.sort((a, b) => {
        let valueA, valueB;
        
        switch(columnIndex) {
            case 1: // Name
                valueA = a.jmeno.toLowerCase();
                valueB = b.jmeno.toLowerCase();
                break;
            case 2: // Position
                valueA = a.pozice.toLowerCase();
                valueB = b.pozice.toLowerCase();
                break;
            case 3: // Department
                valueA = a.oddeleni.toLowerCase();
                valueB = b.oddeleni.toLowerCase();
                break;
            case 4: // Phone
                valueA = a.telefon || '';
                valueB = b.telefon || '';
                break;
            case 5: // Email
                valueA = a.email || '';
                valueB = b.email || '';
                break;
            default:
                return 0;
        }
        
        if (sortDirection === 'asc') {
            return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
        } else {
            return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
        }
    });
    
    updateTableView();
    
    // Update sort indicators
    document.querySelectorAll('th i').forEach(icon => {
        icon.className = 'fas fa-sort';
    });
    
    const currentHeader = document.querySelectorAll('th')[columnIndex];
    const icon = currentHeader.querySelector('i');
    if (icon) {
        icon.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    }
}

// Org Chart View
function updateOrgChartView() {
    console.log('Updating org chart view with', filteredData.length, 'employees');
    
    const topManagersLevel = document.getElementById('top-managers-level');
    const departmentsLevel = document.getElementById('departments-level');
    const employeesLevel = document.getElementById('employees-level');
    
    if (!topManagersLevel || !departmentsLevel || !employeesLevel) {
        console.error('Org chart elements not found');
        return;
    }
    
    topManagersLevel.innerHTML = '';
    departmentsLevel.innerHTML = '';
    employeesLevel.innerHTML = '';
    
    // Define organizational structure
    const orgStructure = {
        'Puchel Michal': {
            departments: [
                'Veřejné zakázky a právní servis',
                'Kancelář PAS', 
                'ICT služby',
                'Správa záruk původu a čistá mobilita'
            ],
            color: 'manager'
        },
        'Chemišinec Igor': {
            departments: [
                'Bilance elektřiny',
                'Bilance plynu', 
                'Energetické trhy',
                'Rozvoj trhu',
                'Smluvní vztahy a povolenky'
            ],
            color: 'manager'
        },
        'Lobotková Alena': {
            departments: [
                'Účetnictví a daně',
                'Financování a controlling',
                'Správa majetku a služeb', 
                'POZE'
            ],
            color: 'manager'
        }
    };
    
    // Add top managers
    Object.keys(orgStructure).forEach(managerName => {
        const manager = filteredData.find(emp => emp.jmeno === managerName);
        if (manager) {
            const box = document.createElement('div');
            box.className = 'position-box ' + orgStructure[managerName].color;
            box.innerHTML = `
                <h4>${manager.jmeno}</h4>
                <small>${manager.pozice}</small>
                <div style="font-size: 0.8em; margin-top: 0.5rem;">${manager.oddeleni}</div>
            `;
            box.onclick = () => openEmployeeModal(manager.jmeno);
            topManagersLevel.appendChild(box);
        }
    });
    
    // Add departments
    Object.keys(orgStructure).forEach(managerName => {
        const managerData = orgStructure[managerName];
        managerData.departments.forEach(deptName => {
            const deptEmployees = filteredData.filter(emp => emp.oddeleni === deptName);
            if (deptEmployees.length > 0) {
                const box = document.createElement('div');
                box.className = 'position-box department';
                box.innerHTML = `
                    <h4>${deptName}</h4>
                    <small>${deptEmployees.length} zaměstnanců</small>
                `;
                departmentsLevel.appendChild(box);
            }
        });
    });
    
    // Add employees grouped by departments
    Object.keys(orgStructure).forEach(managerName => {
        const managerData = orgStructure[managerName];
        managerData.departments.forEach(deptName => {
            const deptEmployees = filteredData.filter(emp => 
                emp.oddeleni === deptName && emp.jmeno !== managerName
            );
            
            deptEmployees.forEach(employee => {
                const box = document.createElement('div');
                box.className = 'position-box employee';
                box.innerHTML = `
                    <h4>${employee.jmeno}</h4>
                    <small>${employee.pozice}</small>
                `;
                box.onclick = () => openEmployeeModal(employee.jmeno);
                employeesLevel.appendChild(box);
            });
        });
    });
    
    console.log('Org chart populated with proper hierarchy');
    
    // Update quick stats after org chart is populated
    updateQuickStats();
}

// Export Functions
function initializeExportFunctions() {
    // Export functions are handled by onclick attributes in HTML
}

function exportToExcel() {
    // Create CSV data (Excel can open CSV files)
    const csvData = convertToCSV(filteredData);
    downloadFile(csvData, 'adresar_ote.csv', 'text/csv');
    showNotification('Export do Excel byl spuštěn', 'success');
}

function exportToCSV() {
    const csvData = convertToCSV(filteredData);
    downloadFile(csvData, 'adresar_ote.csv', 'text/csv');
    showNotification('CSV soubor byl stažen', 'success');
}

function convertToCSV(data) {
    const headers = ['Jméno', 'Pozice', 'Oddělení', 'Telefon', 'Mobil', 'Email'];
    const csvContent = [
        headers.join(','),
        ...data.map(emp => [
            `"${emp.jmeno}"`,
            `"${emp.pozice}"`,
            `"${emp.oddeleni}"`,
            `"${emp.telefon || ''}"`,
            `"${emp.mobil || ''}"`,
            `"${emp.email || ''}"`
        ].join(','))
    ].join('\n');
    
    return '\ufeff' + csvContent; // BOM for proper Czech characters
}

function downloadFile(content, fileName, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

function printDirectory() {
    // Switch to table view for better printing
    const originalView = desktopCurrentView;
    switchView('table');
    
    // Wait for table to render then print
    setTimeout(() => {
        window.print();
        // Switch back to original view
        setTimeout(() => switchView(originalView), 1000);
    }, 500);
    
    showNotification('Tisk byl spuštěn', 'info');
}

function exportVCards() {
    const vCardData = filteredData.map(emp => createVCard(emp)).join('\n\n');
    downloadFile(vCardData, 'kontakty_ote.vcf', 'text/vcard');
    showNotification('Kontakty byly exportovány', 'success');
}

function createVCard(employee) {
    return `BEGIN:VCARD
VERSION:3.0
FN:${employee.jmeno}
ORG:OTE;${employee.oddeleni}
TITLE:${employee.pozice}
TEL;TYPE=WORK:+420 ${employee.telefon}
TEL;TYPE=CELL:+420 ${employee.mobil}
EMAIL:${employee.email}
END:VCARD`;
}

// Dashboard and Statistics
function initializeDashboard() {
    updateDashboardStats();
    document.getElementById('lastUpdated').textContent = new Date().toLocaleDateString('cs-CZ');
}

function updateDashboardStats() {
    if (!allEmployeesData.length) return;
    
    const totalEmployees = allEmployeesData.length;
    const departments = [...new Set(allEmployeesData.map(emp => emp.oddeleni))].length;
    const managers = allEmployeesData.filter(emp => 
        emp.pozice.toLowerCase().includes('vedoucí') ||
        emp.pozice.toLowerCase().includes('ředitel') ||
        emp.pozice.toLowerCase().includes('předseda')
    ).length;
    
    document.getElementById('totalEmployees').textContent = totalEmployees;
    document.getElementById('totalDepartments').textContent = departments;
    document.getElementById('totalManagers').textContent = managers;
    document.getElementById('lastUpdated').textContent = new Date().toLocaleDateString('cs-CZ');
}

function updateQuickStats() {
    const displayedCount = filteredData ? filteredData.length : 0;
    const phoneCount = filteredData.filter(emp => emp.telefon && emp.telefon.trim() !== '').length;
    const mobileCount = filteredData.filter(emp => emp.mobil && emp.mobil.trim() !== '').length;
    
    document.getElementById('displayedCount').textContent = displayedCount;
    document.getElementById('phoneCount').textContent = phoneCount;
    document.getElementById('mobileCount').textContent = mobileCount;
    
    // Update current selection
    const searchText = document.getElementById('mainSearchInput').value;
    const selectedDepartment = document.getElementById('departmentFilter').value;
    const activeChips = document.querySelectorAll('.filter-chip.active').length;
    
    let selectionText = 'Všichni';
    if (searchText || selectedDepartment || activeChips > 0) {
        selectionText = 'Filtrováno';
    }
    document.getElementById('currentSelection').textContent = selectionText;
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
}

// Helper functions for existing functionality
function openEmployeeModal(employeeName) {
    // This should integrate with existing modal functionality
    if (typeof openModal === 'function') {
        const employee = allEmployeesData.find(emp => emp.jmeno === employeeName);
        if (employee) {
            openModal(employee);
        }
    } else if (typeof window.openModal === 'function') {
        const employee = allEmployeesData.find(emp => emp.jmeno === employeeName);
        if (employee) {
            window.openModal(employee);
        }
    } else {
        console.log('Modal function not available, employee:', employeeName);
    }
}

function callEmployee(phoneNumber) {
    if (phoneNumber) {
        window.location.href = `tel:+420${phoneNumber.replace(/\s+/g, '')}`;
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    kbd {
        background: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
        font-family: monospace;
        color: #374151;
    }
    
    .dark-mode kbd {
        background: #374151;
        border-color: #6b7280;
        color: #e5e7eb;
    }
`;
document.head.appendChild(style);

// Export functions to global scope for HTML onclick handlers
window.switchView = switchView;
window.sortTable = sortTable;
window.exportToExcel = exportToExcel;
window.exportToCSV = exportToCSV;
window.printDirectory = printDirectory;
window.exportVCards = exportVCards;
window.applySavedSearch = applySavedSearch;
window.openEmployeeModal = openEmployeeModal;
window.callEmployee = callEmployee;

console.log('Desktop enhancements loaded'); 