let currentView = 'employees';
let mapInitialized = false;
let selectedDepartment = null;

function removeDiacritics(str) {
    return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

function sortEmployeesAlphabetically(employeeList) {
    return employeeList.sort((a, b) => {

        const lastNameA = removeDiacritics(a.jmeno.trim().split(' ')[0].toLowerCase());
        const lastNameB = removeDiacritics(b.jmeno.trim().split(' ')[0].toLowerCase());
        return lastNameA.localeCompare(lastNameB, 'cs', { numeric: true });
    });
}

function sortEmployeesHierarchically(employeeList) {
    return employeeList.sort((a, b) => {
        const isManagerA = a.pozice.toLowerCase().includes('vedoucí') ||
                          a.pozice.toLowerCase().includes('ředitel') ||
                          a.pozice.toLowerCase().includes('předseda') ||
                          a.pozice.toLowerCase().includes('místopředseda');
        const isManagerB = b.pozice.toLowerCase().includes('vedoucí') ||
                          b.pozice.toLowerCase().includes('ředitel') ||
                          b.pozice.toLowerCase().includes('předseda') ||
                          b.pozice.toLowerCase().includes('místopředseda');

        if (isManagerA && !isManagerB) return -1;
        if (!isManagerA && isManagerB) return 1;

        const lastNameA = removeDiacritics(a.jmeno.trim().split(' ')[0].toLowerCase());
        const lastNameB = removeDiacritics(b.jmeno.trim().split(' ')[0].toLowerCase());
        return lastNameA.localeCompare(lastNameB, 'cs', { numeric: true });
    });
}

function switchToEmployees() {
    console.log('Switching to employees view');
    currentView = 'employees';

    const employeeListBtn = document.getElementById('employeeListBtn');
    const officeMapBtn = document.getElementById('officeMapBtn');
    const employeeSection = document.getElementById('employeeSection');
    const mapSection = document.getElementById('mapSection');
    const pageTitle = document.getElementById('pageTitle');

    if (employeeListBtn && officeMapBtn && employeeSection && mapSection && pageTitle) {
        employeeListBtn.classList.add('active');
        officeMapBtn.classList.remove('active');

        pageTitle.textContent = 'Adresář OTE';

        mapSection.style.display = 'none';
        employeeSection.style.display = 'block';

        document.body.classList.remove('map-view');

        console.log('Successfully switched to employees view');
    } else {
        console.error('Some elements not found for employees view switch');
    }
}

function switchToMap() {
    console.log('Switching to map view');
    currentView = 'map';

    const employeeListBtn = document.getElementById('employeeListBtn');
    const officeMapBtn = document.getElementById('officeMapBtn');
    const employeeSection = document.getElementById('employeeSection');
    const mapSection = document.getElementById('mapSection');
    const pageTitle = document.getElementById('pageTitle');

    if (employeeListBtn && officeMapBtn && employeeSection && mapSection && pageTitle) {
        officeMapBtn.classList.add('active');
        employeeListBtn.classList.remove('active');

        pageTitle.textContent = 'Mapa rozmístění pracovišť';


        employeeSection.style.display = 'none';
        mapSection.style.display = 'block';

        document.body.classList.add('map-view');


        initializeDepartmentsPanel();

        if (!mapInitialized) {
            console.log('Initializing map for first time');
            if (typeof window.initializeMap === 'function') {
                window.initializeMap();
                mapInitialized = true;
            } else {
                console.log('Waiting for map initialization...');
                const checkInitialize = setInterval(() => {
                    if (typeof window.initializeMap === 'function') {
                        clearInterval(checkInitialize);
                        window.initializeMap();
                        mapInitialized = true;
                    }
                }, 50);
            }
        }

        console.log('Successfully switched to map view');
    } else {
        console.error('Some elements not found for map view switch');
    }
}

window.switchToEmployees = switchToEmployees;
window.switchToMap = switchToMap;

document.addEventListener('DOMContentLoaded', function() {
    console.log('Map integration script loaded!');
    console.log('window.employeesData available:', typeof window.employeesData !== 'undefined');
    if (typeof window.employeesData !== 'undefined') {
        console.log('window.employeesData length:', window.employeesData.length);
    }

    setTimeout(function() {
        console.log('Initializing map integration...');
        initializeMapIntegration();
    }, 100);
});

function initializeMapIntegration() {
    console.log('initializeMapIntegration started');
    console.log('window.employeesData check:', typeof window.employeesData !== 'undefined');


    function loadEmployeesData() {
        return new Promise((resolve) => {
            function checkData() {
                if (typeof window.employeesData !== 'undefined' && window.employeesData.length > 0) {
                    console.log('Employee data loaded:', window.employeesData.length, 'employees');
                    resolve();
                } else {
                    console.log('Waiting for employee data...');
                    setTimeout(checkData, 100);
                }
            }
            checkData();
        });
    }

    function normalizeName(str) {
        return str
            .normalize('NFD')
            .replace(/\p{Diacritic}/gu, '')
            .toLowerCase()
            .replace(/\s+/g, ' ')
            .trim();
    }

    function detectGender(fullName) {
        return 'male'; 
    }




    
    function initializeMap() {
        console.log('Inicializace mapy...');
        console.log('Inicializace s', window.employeesData.length, 'zaměstnanci');

        selectedDepartment = 'all';

        const mapImg = document.getElementById('office-map-img');
        const errorDiv = document.getElementById('map-error');

        // Wait for image to load before rendering markers
        if (mapImg.complete) {
            // Image is already loaded
            initializeMapContent();
        } else {
            // Wait for image to load
            mapImg.onload = function() {
                errorDiv.style.display = 'none';
                initializeMapContent();
            };

            mapImg.onerror = function() {
                errorDiv.style.display = 'block';
            };
        }
    }

    function initializeMapContent() {
        renderEmployeeList(window.employeesData);
        renderMarkers(window.employeesData);
        setupMapSearch();
        setupMapToggle();
        updateDepartmentTitle('all');

        console.log('Mapa byla úspěšně inicializována');
    }

    
    window.initializeMap = initializeMap;

    
    function renderEmployeeList(list) {
        const ul = document.getElementById('mapEmployeeList');
        if (!ul) {
            console.error('Element mapEmployeeList nebyl nalezen');
            return;
        }

        console.log('Rendering employee list, počet:', list ? list.length : 'undefined');
        console.log('List data:', list);

        
        ul.style.opacity = '0.5';
        ul.style.transform = 'translateY(10px)';

        setTimeout(() => {
            ul.innerHTML = '';

            
            const sortedList = [...list];

            console.log('Vytvářím elementy pro', sortedList.length, 'zaměstnanců');
            sortedList.forEach((z, index) => {
                const li = document.createElement('li');
                li.tabIndex = 0;
                li.dataset.jmeno = z.jmeno;

                const avatar = document.createElement('img');
                avatar.src = z.obrazek || 'img/no-person-photo.png';
                avatar.alt = z.jmeno;
                avatar.className = 'avatar-img';
                avatar.onerror = () => { avatar.src = 'img/no-person-photo.png'; };
                li.appendChild(avatar);

                const infoContainer = document.createElement('div');
                infoContainer.className = 'employee-info';

                const nameElement = document.createElement('h3');
                const nameParts = z.jmeno.split(' ');
                if (nameParts.length >= 2) {
                    nameElement.innerHTML = nameParts[0] + '<br>' + nameParts.slice(1).join(' ');
                } else {
                    nameElement.textContent = z.jmeno;
                }
                nameElement.className = 'emp-name';
                nameElement.title = z.jmeno;
                infoContainer.appendChild(nameElement);

                li.appendChild(infoContainer);

                const btn = document.createElement('button');
                btn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Najít';
                btn.className = 'find-btn';
                btn.title = 'Zobrazit na mapě';
                btn.onclick = e => { e.stopPropagation(); highlightByName(z.jmeno, true); };
                li.appendChild(btn);

                li.onclick = e => {
                    e.preventDefault();
                    highlightByName(z.jmeno, true);
                };

                li.addEventListener('keydown', e => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        highlightByName(z.jmeno, true);
                    }
                });

                li.style.opacity = '0';
                li.style.transform = 'translateX(-20px)';
                ul.appendChild(li);
                console.log('Přidán element pro:', z.jmeno);

                setTimeout(() => {
                    li.style.transition = 'all 0.3s ease';
                    li.style.opacity = '1';
                    li.style.transform = 'translateX(0)';
                }, index * 50);
            });

            setTimeout(() => {
                ul.style.transition = 'all 0.3s ease';
                ul.style.opacity = '1';
                ul.style.transform = 'translateY(0)';
                console.log('Seznam zaměstnanců vykreslen, počet elementů:', ul.children.length);
            }, 100);
        }, 150);
    }

   
    function renderMarkers(list) {
        const container = document.querySelector('.office-map-container');
        if (!container) {
            console.error('Map container not found');
            return;
        }

        container.querySelectorAll('.marker').forEach(m => m.remove());

        // Get the map image to calculate scaling
        const mapImg = container.querySelector('img');
        if (!mapImg) {
            console.error('Map image not found');
            return;
        }

        // Calculate scaling factor based on actual vs original image size
        const originalWidth = 1920; // Original map width
        const actualWidth = mapImg.clientWidth || mapImg.offsetWidth;
        const scaleFactor = actualWidth / originalWidth;

        let markersCreated = 0;
        list.forEach((employee) => {

            if (!employee.left || !employee.top) {
                console.log(`Zaměstnanec ${employee.jmeno} nemá definované pozice na mapě`);
                return;
            }
            markersCreated++;

            const marker = document.createElement('div');

            const genderType = detectGender(employee.jmeno);
            marker.className = `marker ${genderType}`;

            // Scale positions based on image size
            const scaledTop = employee.top * scaleFactor;
            const scaledLeft = employee.left * scaleFactor;

            marker.style.top = scaledTop + 'px';
            marker.style.left = scaledLeft + 'px';
            marker.dataset.jmeno = employee.jmeno;
            marker.tabIndex = 0;

            const genderDot = document.createElement('div');
            genderDot.style.width = '26px';
            genderDot.style.height = '26px';
            genderDot.style.borderRadius = '50%';
            genderDot.style.background = genderType === 'female' ? '#ec4899' : '#3b82f6';
            genderDot.style.boxShadow = genderType === 'female' ?
                '0 2px 8px rgba(236, 72, 153, 0.3)' :
                '0 2px 8px rgba(59, 130, 246, 0.3)';
            genderDot.style.border = '2.5px solid #fff';
            genderDot.style.display = 'block';
            genderDot.style.transition = 'all 0.3s ease';
            genderDot.style.position = 'absolute';
            genderDot.style.top = '50%';
            genderDot.style.left = '50%';
            genderDot.style.transform = 'translate(-50%, -50%)';
            genderDot.title = `${employee.jmeno} (${genderType === 'female' ? 'Žena' : 'Muž'})`;
            marker.appendChild(genderDot);

            const tooltip = document.createElement('span');
            tooltip.className = 'tooltip';
            tooltip.innerHTML = employee.jmeno;
            marker.appendChild(tooltip);

            marker.addEventListener('click', () => {
                highlightByName(employee.jmeno, true);
            });

            container.appendChild(marker);
        });

        console.log(`Vytvořeno markerů: ${markersCreated} z ${list.length} zaměstnanců`);
        console.log('Markery na mapě:', container.querySelectorAll('.marker').length);

        // Add resize listener to recalculate positions
        if (!window.mapResizeListenerAdded) {
            window.addEventListener('resize', debounce(() => {
                updateMarkerPositions();
            }, 250));
            window.mapResizeListenerAdded = true;
        }
    }

    // Function to update marker positions on resize
    function updateMarkerPositions() {
        const container = document.querySelector('.office-map-container');
        const mapImg = container?.querySelector('img');
        if (!container || !mapImg) return;

        const originalWidth = 1920;
        const actualWidth = mapImg.clientWidth || mapImg.offsetWidth;
        const scaleFactor = actualWidth / originalWidth;

        container.querySelectorAll('.marker').forEach(marker => {
            const employeeName = marker.dataset.jmeno;
            const employee = window.employeesData?.find(emp => emp.jmeno === employeeName);

            if (employee && employee.left && employee.top) {
                const scaledTop = employee.top * scaleFactor;
                const scaledLeft = employee.left * scaleFactor;

                marker.style.top = scaledTop + 'px';
                marker.style.left = scaledLeft + 'px';
            }
        });
    }

    // Debounce function to limit resize events
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    
    function highlightByName(jmeno, scrollTo) {
        
        clearAllHighlights();

        const activeMarker = findAndHighlightMarker(jmeno, scrollTo);

      
        const activeEmployee = findAndHighlightEmployee(jmeno, scrollTo);

        
        if (activeEmployee || activeMarker) {
            showFoundAnimation(jmeno);
        }
    }

  
    function clearAllHighlights() {
        document.querySelectorAll('.marker').forEach(marker => {
            marker.classList.remove('active', 'found-pulse');
        });
        document.querySelectorAll('#mapEmployeeList li').forEach(li => {
            li.classList.remove('active', 'found-highlight');
        });
    }

    function findAndHighlightMarker(jmeno, scrollTo) {
        let foundMarker = null;
        document.querySelectorAll('.marker').forEach(marker => {
            const isActive = marker.dataset.jmeno === jmeno;
            if (isActive) {
                foundMarker = marker;
                marker.classList.add('active', 'found-pulse');

                if (scrollTo) {
                    setTimeout(() => {
                        marker.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'center'
                        });

                        setTimeout(() => {
                            marker.classList.add('extra-pulse');
                            setTimeout(() => marker.classList.remove('extra-pulse'), 2000);
                        }, 800);
                    }, 300);
                }
            }
        });
        return foundMarker;
    }

    function findAndHighlightEmployee(jmeno, scrollTo) {
        let foundEmployee = null;
        document.querySelectorAll('#mapEmployeeList li').forEach(li => {
            const isActive = li.dataset.jmeno === jmeno;
            if (isActive) {
                foundEmployee = li;
                li.classList.add('active', 'found-highlight');

                if (scrollTo) {
                    setTimeout(() => {
                        li.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        });
                    }, 100);
                }
            }
        });
        return foundEmployee;
    }

    function showFoundAnimation(jmeno) {
        console.log('Nalezen:', jmeno);
    }

    function setupMapSearch() {
        const searchInput = document.getElementById('mapSearchInput');
        const searchClearBtn = document.getElementById('mapSearchClearBtn');
        let searchTimeout;

        if (!searchInput || !searchClearBtn) {
            console.log('Search elements not found');
            return;
        }

        function updateClearButton() {
            if (searchInput.value.trim()) {
                searchClearBtn.classList.add('visible');
            } else {
                searchClearBtn.classList.remove('visible');
            }
        }

        function updateResultsCount(count, isSearching) {
            const resultsCount = document.getElementById('mapSearchResultsCount');
            const resultsText = document.getElementById('mapResultsCountText');

            if (resultsCount && resultsText) {
                if (isSearching) {
                    resultsText.textContent = count === 1 ? '1 výsledek' : `${count} výsledků`;
                    resultsCount.classList.add('visible');
                } else {
                    resultsCount.classList.remove('visible');
                }
            }
        }

        searchInput.addEventListener('input', function() {
            const val = this.value.toLowerCase().trim();
            updateClearButton();

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (val === '') {
                    const sortedEmployees = sortEmployeesAlphabetically([...window.employeesData]);
                    renderEmployeeList(sortedEmployees);
                    updateResultsCount(sortedEmployees.length, false);
                } else {
                    let employeesToSearch;
                    if (selectedDepartment === 'all') {
                        employeesToSearch = window.employeesData;
                    } else {
                        employeesToSearch = window.employeesData.filter(employee => {
                            return employee.oddeleni.split(',').map(d => d.trim()).includes(selectedDepartment);
                        });
                    }

                    const filtered = employeesToSearch.filter(z => {
                        const name = normalizeName(z.jmeno);
                        const position = normalizeName(z.pozice);
                        const department = normalizeName(z.oddeleni);
                        const searchTerm = normalizeName(val);
                        
                        return name.includes(searchTerm) || 
                               position.includes(searchTerm) || 
                               department.includes(searchTerm);
                    });


                    const sortedFiltered = sortEmployeesAlphabetically(filtered);
                    renderEmployeeList(sortedFiltered);
                    updateResultsCount(sortedFiltered.length, true);

                    if (filtered.length === 1) {
                        setTimeout(() => {
                            highlightByName(filtered[0].jmeno, true);
                        }, 500);
                    } else if (filtered.length === 0 && val.length > 2) {
                        showMapNotFoundMessage(val, selectedDepartment);
                    }
                }
            }, 200);
        });

        searchClearBtn.addEventListener('click', function() {
            searchInput.value = '';
            updateClearButton();

            let employeesToShow;
            if (selectedDepartment === 'all') {
                employeesToShow = [...window.employeesData];
            } else {
                employeesToShow = window.employeesData.filter(employee => {
                    return employee.oddeleni.split(',').map(d => d.trim()).includes(selectedDepartment);
                });
            }

            const sortedEmployees = sortEmployeesAlphabetically(employeesToShow);
            renderEmployeeList(sortedEmployees);
            updateResultsCount(sortedEmployees.length, false);
            searchInput.focus();
        });
    }



    function setupMapToggle() {
        const toggleBtns = document.querySelectorAll('.toggle-btn');
        const mapContainer = document.querySelector('.office-map-container');

        if (!toggleBtns.length || !mapContainer) {
            console.log('Toggle buttons or map container not found');
            return;
        }

        toggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.getAttribute('data-view');

              
                toggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

               
                if (view === 'single') {
                    mapContainer.classList.add('single-view');
                } else {
                    mapContainer.classList.remove('single-view');
                }

                console.log('Přepnuto na režim:', view);
            });
        });
    }

    
    window.findEmployeeOnMap = function(employeeName) {
        if (currentView !== 'map') {
            switchToMap();
            setTimeout(() => findEmployeeOnMap(employeeName), 500);
            return;
        }

        highlightByName(employeeName, true);
    };

   
    function showEmployeeDetails(employee) {
      
        if (typeof window.openModal === 'function') {
            window.openModal(employee);
        }
    }

  

    
    function showSearchHint(jmeno) {
        const hint = document.createElement('div');
        hint.className = 'search-hint';
        hint.innerHTML = `
            <i class="fas fa-lightbulb"></i>
            Automaticky nalezen: <strong>${jmeno}</strong>
        `;

        document.body.appendChild(hint);

        setTimeout(() => hint.classList.add('show'), 100);
        setTimeout(() => {
            hint.classList.remove('show');
            setTimeout(() => hint.remove(), 300);
        }, 2500);
    }

    function showMapNotFoundMessage(searchTerm, currentDepartment = 'all') {
        const message = document.createElement('div');
        message.className = 'not-found-message';

        
        let messageText;
        if (currentDepartment === 'all') {
            messageText = `Nenalezen zaměstnanec: "<strong>${searchTerm}</strong>"`;
        } else {
            messageText = `Zaměstnanec nenalezen v oddělení "<strong>${currentDepartment}</strong>". <br>
                          <small>Přepněte na "Všichni zaměstnanci" pro vyhledávání napříč všemi odděleními.</small>`;
        }

        message.innerHTML = `
            <i class="fas fa-search"></i>
            <div class="message-content">
                <div class="message-text">${messageText}</div>
                ${currentDepartment !== 'all' ? `
                    <button class="switch-to-all-btn-map" onclick="switchToAllEmployeesMap()">
                        <i class="fas fa-users"></i>
                        Vyhledat ve všech odděleních
                    </button>
                ` : ''}
            </div>
        `;

        document.body.appendChild(message);

        setTimeout(() => message.classList.add('show'), 100);
        setTimeout(() => {
            message.classList.remove('show');
            setTimeout(() => message.remove(), 300);
        }, 10000);
    }

    function switchToAllEmployeesMap() {
        const allEmployeesButton = document.querySelector('#departmentsList .department-item[data-department="all"]');
        if (allEmployeesButton) {
            allEmployeesButton.click();
        }

        const existingMessage = document.querySelector('.not-found-message');
        if (existingMessage) {
            existingMessage.classList.remove('show');
            setTimeout(() => existingMessage.remove(), 300);
        }
    }

    window.switchToAllEmployeesMap = switchToAllEmployeesMap;

    loadEmployeesData().then(() => {
        console.log('Data načtena, inicializace dokončena');
        console.log('Počet zaměstnanců:', window.employeesData ? window.employeesData.length : 'undefined');
        window.switchToEmployees();
    });

function initializeDepartmentsPanel() {
    if (!window.employeesData) {
        console.log('Data zaměstnanců nejsou k dispozici');
        return;
    }

    const departmentsList = document.getElementById('departmentsList');
    if (!departmentsList) {
        console.log('Element departmentsList nenalezen');
        return;
    }

    const departments = {};
    window.employeesData.forEach(employee => {
        const deptString = employee.oddeleni;
        const depts = deptString.split(',').map(d => d.trim());

        depts.forEach(dept => {
            if (!departments[dept]) {
                departments[dept] = [];
            }
            departments[dept].push(employee);
        });
    });

    const sortedDepartments = Object.keys(departments).sort();

    departmentsList.innerHTML = '';

    const allItem = document.createElement('div');
    allItem.className = 'department-item all-employees active';
    allItem.setAttribute('data-department', 'all');
    allItem.innerHTML = `
        <div class="department-name">Všichni zaměstnanci</div>
        <div class="department-count">${window.employeesData.length} zaměstnanců</div>
    `;
    allItem.addEventListener('click', () => selectDepartment('all', allItem));
    departmentsList.appendChild(allItem);

    selectedDepartment = 'all';

    sortedDepartments.forEach(dept => {
        const sortedEmployees = sortEmployeesHierarchically([...departments[dept]]);

        departments[dept] = sortedEmployees;

        const item = document.createElement('div');
        item.className = 'department-item';
        item.setAttribute('data-department', dept);
        item.innerHTML = `
            <div class="department-name">${dept}</div>
            <div class="department-count">${departments[dept].length} zaměstnanců</div>
        `;
        item.addEventListener('click', () => selectDepartment(dept, item));
        departmentsList.appendChild(item);
    });
}

function selectDepartment(department, element) {
    document.querySelectorAll('.department-item').forEach(item => {
        item.classList.remove('active');
    });

    element.classList.add('active');

    selectedDepartment = department;

    updateDepartmentTitle(department);

    let filteredEmployees;
    if (department === 'all') {
        filteredEmployees = [...window.employeesData];
        filteredEmployees = sortEmployeesAlphabetically(filteredEmployees);
    } else {
        filteredEmployees = window.employeesData.filter(employee => {
            return employee.oddeleni.split(',').map(d => d.trim()).includes(department);
        });
        
        filteredEmployees = sortEmployeesHierarchically(filteredEmployees);
    }

    
    renderEmployeeList(filteredEmployees);

   
    highlightDepartmentOnMap(department);
}


function highlightDepartmentOnMap(department) {
    console.log(`=== FILTROVÁNÍ ODDĚLENÍ: ${department} ===`);

    const markers = document.querySelectorAll('.marker');
    console.log(`Nalezeno ${markers.length} markerů na mapě`);

    let visibleCount = 0;
    let hiddenCount = 0;

    markers.forEach((marker, index) => {
        const employeeName = marker.getAttribute('data-jmeno');
        const employee = window.employeesData.find(emp => emp.jmeno === employeeName);

        console.log(`Marker ${index + 1}: ${employeeName}, oddělení: ${employee ? employee.oddeleni : 'NENALEZEN'}`);

        if (department === 'all') {
            
            marker.style.display = 'block';
            marker.style.visibility = 'visible';
            marker.style.opacity = '1';
            marker.style.transform = 'scale(1)';
            marker.style.filter = 'none';
            visibleCount++;
            console.log(`  → ZOBRAZUJI (všichni)`);
        } else if (employee && employee.oddeleni.split(',').map(d => d.trim()).includes(department)) {
           
            marker.style.display = 'block';
            marker.style.visibility = 'visible';
            marker.style.opacity = '1';
            marker.style.transform = 'scale(1.1)';
            marker.style.filter = 'brightness(1.1) saturate(1.2)';
            visibleCount++;
            console.log(`  → ZOBRAZUJI (z oddělení ${department})`);
        } else {
            
            marker.style.display = 'none';
            marker.style.visibility = 'hidden';
            marker.style.opacity = '0';
            hiddenCount++;
            console.log(`  → SKRÝVÁM (jiné oddělení)`);
        }
    });

    console.log(`Výsledek: ${visibleCount} zobrazeno, ${hiddenCount} skryto`);
    console.log(`=== KONEC FILTROVÁNÍ ===`);

    
    filterEmployeeList(department);
}


function filterEmployeeList(department) {
    const employeeList = document.getElementById('mapEmployeeList');
    if (!employeeList) return;

    const listItems = employeeList.querySelectorAll('li');

    listItems.forEach(item => {
        const employeeName = item.getAttribute('data-jmeno');
        const employee = window.employeesData.find(emp => emp.jmeno === employeeName);

        if (department === 'all') {
            
            item.style.display = 'flex';
            item.style.opacity = '1';
        } else if (employee && employee.oddeleni.split(',').map(d => d.trim()).includes(department)) {
            
            item.style.display = 'flex';
            item.style.opacity = '1';
        } else {
            
            item.style.display = 'none';
        }
    });
}


function highlightEmployeeOnMap(employeeName) {
    
    const employee = window.employeesData.find(emp => emp.jmeno === employeeName);
    if (!employee) return;

    const firstDepartment = employee.oddeleni.split(',')[0].trim();

    const departmentItem = document.querySelector(`[data-department="${firstDepartment}"]`);
    if (departmentItem) {
        selectDepartment(firstDepartment, departmentItem);
    }

    setTimeout(() => {
        highlightByName(employeeName, true);
    }, 300);
}

function updateDepartmentTitle(department) {
    const mapCurrentSection = document.getElementById('mapCurrentSection');

    if (mapCurrentSection) {
        if (department === 'all') {
            mapCurrentSection.textContent = 'Všichni zaměstnanci';
        } else {
            mapCurrentSection.textContent = department;
        }
    }
}

window.initializeDepartmentsPanel = initializeDepartmentsPanel;
window.highlightEmployeeOnMap = highlightEmployeeOnMap;
window.updateDepartmentTitle = updateDepartmentTitle;
}
