<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Map Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-title {
            color: #333;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .dark-mode-toggle {
            background: #1f2937;
            color: white;
        }
        
        .dark-mode-toggle:hover {
            background: #374151;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test Map Fixes</h1>
        <p>Tento soubor slouží k testování oprav mapy. Klikněte na tlačítka níže pro testování různých funkcí.</p>
        
        <div class="test-section">
            <div class="test-title">1. Test Dark Mode pro markery</div>
            <p>Testuje, zda se markery správně zobrazují v dark mode.</p>
            <button class="test-button dark-mode-toggle" onclick="toggleDarkMode()">Přepnout Dark Mode</button>
            <button class="test-button" onclick="openMapPage()">Otevřít mapu</button>
            <div id="darkmode-status" class="status info">Dark mode: Vypnutý</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. Test responzivních pozic markerů</div>
            <p>Testuje, zda se pozice markerů správně škálují při změně velikosti okna.</p>
            <button class="test-button" onclick="testResponsivePositions()">Test responzivních pozic</button>
            <button class="test-button" onclick="simulateResize()">Simulovat změnu velikosti</button>
            <div id="responsive-status" class="status info">Připraveno k testování</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. Test vzdáleností navigace</div>
            <p>Testuje, zda jsou vzdálenosti mezi navigací, oddělením a mapou sjednocené.</p>
            <button class="test-button" onclick="testNavigation()">Test navigace</button>
            <button class="test-button" onclick="measureDistances()">Změřit vzdálenosti</button>
            <div id="navigation-status" class="status info">Připraveno k testování</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. Celkový test</div>
            <p>Spustí všechny testy najednou.</p>
            <button class="test-button" onclick="runAllTests()">Spustit všechny testy</button>
            <div id="overall-status" class="status info">Připraveno k testování</div>
        </div>
    </div>

    <script>
        let isDarkMode = false;
        
        function toggleDarkMode() {
            isDarkMode = !isDarkMode;
            document.body.classList.toggle('dark-mode', isDarkMode);
            
            const status = document.getElementById('darkmode-status');
            status.textContent = `Dark mode: ${isDarkMode ? 'Zapnutý' : 'Vypnutý'}`;
            status.className = `status ${isDarkMode ? 'success' : 'info'}`;
            
            // Apply dark mode to the page
            if (isDarkMode) {
                document.body.style.background = '#1f2937';
                document.body.style.color = '#f9fafb';
                document.querySelector('.test-container').style.background = '#374151';
                document.querySelector('.test-container').style.color = '#f9fafb';
            } else {
                document.body.style.background = '#f5f5f5';
                document.body.style.color = '#000';
                document.querySelector('.test-container').style.background = 'white';
                document.querySelector('.test-container').style.color = '#000';
            }
        }
        
        function openMapPage() {
            window.open('index.html', '_blank');
        }
        
        function testResponsivePositions() {
            const status = document.getElementById('responsive-status');
            status.textContent = 'Testování responzivních pozic... Otevřete mapu a změňte velikost okna.';
            status.className = 'status info';
            
            setTimeout(() => {
                status.textContent = 'Test dokončen. Zkontrolujte, zda se markery správně škálují.';
                status.className = 'status success';
            }, 2000);
        }
        
        function simulateResize() {
            const status = document.getElementById('responsive-status');
            status.textContent = 'Simulace změny velikosti okna...';
            status.className = 'status info';
            
            // Simulate window resize
            window.dispatchEvent(new Event('resize'));
            
            setTimeout(() => {
                status.textContent = 'Simulace dokončena. Zkontrolujte pozice markerů na mapě.';
                status.className = 'status success';
            }, 1000);
        }
        
        function testNavigation() {
            const status = document.getElementById('navigation-status');
            status.textContent = 'Testování navigace... Otevřete mapu a zkontrolujte vzdálenosti.';
            status.className = 'status info';
            
            setTimeout(() => {
                status.textContent = 'Test dokončen. Zkontrolujte sjednocené vzdálenosti.';
                status.className = 'status success';
            }, 2000);
        }
        
        function measureDistances() {
            const status = document.getElementById('navigation-status');
            status.textContent = 'Měření vzdáleností... Otevřete vývojářské nástroje pro detaily.';
            status.className = 'status info';
            
            console.log('Měření vzdáleností navigačních prvků:');
            console.log('- Vyhledávací lišta: top: 100px');
            console.log('- Hlavní obsah: padding-top: 180px');
            console.log('- Panel oddělení: top: 80px');
            
            setTimeout(() => {
                status.textContent = 'Měření dokončeno. Zkontrolujte konzoli pro detaily.';
                status.className = 'status success';
            }, 1500);
        }
        
        function runAllTests() {
            const status = document.getElementById('overall-status');
            status.textContent = 'Spouštění všech testů...';
            status.className = 'status info';
            
            // Run all tests
            testResponsivePositions();
            testNavigation();
            
            setTimeout(() => {
                status.textContent = 'Všechny testy dokončeny! Zkontrolujte jednotlivé sekce.';
                status.className = 'status success';
            }, 3000);
        }
        
        // Add some CSS for dark mode
        const style = document.createElement('style');
        style.textContent = `
            .dark-mode .test-section {
                border-color: #4b5563;
                background: #4b5563;
            }
            
            .dark-mode .status {
                background: #374151;
                color: #f9fafb;
                border-color: #6b7280;
            }
            
            .dark-mode .status.success {
                background: #065f46;
                color: #d1fae5;
            }
            
            .dark-mode .status.error {
                background: #991b1b;
                color: #fee2e2;
            }
            
            .dark-mode .status.info {
                background: #1e40af;
                color: #dbeafe;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
