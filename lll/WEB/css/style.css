* {
    box-sizing: border-box;
    font-family: 'Nunito', sans-serif;
}

html {
    scroll-behavior: smooth;
}

body {
    background: #f8f9fa;
    font-family: 'Nunito', sans-serif;
    color: #222;
    margin: 0;
}

header {
    background-color: #fff;
    box-shadow: 1px 1px 20px rgba(0, 0, 0, 0.1);
    min-width: 100%;
    position: fixed;
    top: 0;
    z-index: 100;
}

.navbar a,
.navbar {
    text-decoration: none;
    color: #000;
    font-weight: 600;
}

.navbar {
    background: #fff;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    padding: 18px 32px;
    display: flex;
    align-items: center;
    gap: 32px;
}

.navbar .logo {
    flex: 0 0 auto;
}

.logo1 {
    height: 48px;
    margin-right: 18px;
}

.header-title {
    font-size: 2rem;
    font-weight: 800;
    color: #2563eb;
    margin: 0 32px 0 0;
}


.main-search-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 0;
    margin-top: 80px;
}

.search-container-main {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.search-wrapper {
    position: relative;
    width: 100%;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.search-input-group:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
}

.search-icon {
    position: absolute;
    left: 20px;
    color: #9ca3af;
    font-size: 18px;
    z-index: 2;
}

.main-search-input {
    width: 100%;
    padding: 20px 60px 20px 60px;
    border: none;
    border-radius: 16px;
    font-size: 18px;
    font-weight: 500;
    background: transparent;
    outline: none;
    color: #1f2937;
}

.main-search-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.search-clear-btn {
    position: absolute;
    right: 20px;
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.search-clear-btn:hover {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}


@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
        text-align: center;
    }

    .main-search-section {
        padding: 1.5rem 0;
    }

    .search-container-main {
        padding: 0 1rem;
    }

    .main-search-input {
        padding: 16px 50px 16px 50px;
        font-size: 16px;
    }

    .view-toggle-header {
        order: -1;
    }
}



.navbar .menu_list {
    display: none;
}

.navbar .logo img {
    max-height: 40px;
}

.header-title {
    font-size: 24px;
    margin: 0;
    text-align: center;
    flex-grow: 1;
    color: #00808f;
}

@media screen and (min-width: 768px) {
    .navbar .menu_list {
        display: flex;
        margin-top: 10px;
        justify-content: space-between;
        width: 40%;
    }
    .navbar .dropdown_list {
        display: none;
    }
    .navbar .dropdown_link {
        display: flex;
    }
    .navbar .dropdown {
        position: relative;
    }
    .navbar .dropdown .dropdown_list {
        display: block;
        position: absolute;
        width: 300px;
        padding: 20px 20px 20px 80px;
        box-shadow: rgba(0, 0, 0, 0.1);
        background-color: #fff;
        top: 50px;
        right: 0;
    }
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    overflow: auto;
    background: rgba(37,99,235,0.10);
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e0f7fa 100%);
    margin: 3% auto;
    padding: 40px;
    border: none;
    border-radius: 24px;
    width: 90%;
    max-width: 650px;
    text-align: left;
    box-shadow:
        0 25px 60px rgba(0,0,0,0.15),
        0 8px 25px rgba(0, 128, 143, 0.1),
        inset 0 1px 0 rgba(255,255,255,0.8);
    animation: modalFadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    backdrop-filter: blur(10px);
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal.closing .modal-content {
    animation: modalFadeOut 0.3s ease-in;
}

@keyframes modalFadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-50px); }
}

.modal-content img {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 25px;
    box-shadow:
        0 12px 35px rgba(0, 128, 143, 0.25),
        0 0 0 4px rgba(255, 255, 255, 0.9),
        0 0 0 6px rgba(0, 128, 143, 0.2);
    border: none;
    transition: all 0.3s ease;
}

.modal-content img:hover {
    transform: scale(1.05);
    box-shadow:
        0 15px 45px rgba(0, 128, 143, 0.35),
        0 0 0 4px rgba(255, 255, 255, 0.9),
        0 0 0 8px rgba(0, 128, 143, 0.3);
}

.modal-content h2 {
    font-size: 2rem;
    font-weight: 900;
    color: #00808f;
    margin: 0 0 20px 0;
    text-align: center;
}

.modal-header {
    width: 100%;
    margin-bottom: 20px;
}

.modal-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 1px solid #00add0;
    border-radius: 10px;
    background-color: #f0f8ff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    width: 100%;
}

.modal-info p {
    font-size: 16px;
    margin: 10px 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-info .icon {
    margin-right: 10px;
    color: #00add0;
    font-size: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #00add0;
    transition: background-color 0.3s ease;
}

.contact-item:hover {
    background-color: #e9ecef;
}

.contact-item .icon {
    color: #00add0;
    font-size: 18px;
    min-width: 20px;
}

.contact-item span {
    font-size: 16px;
    color: #333;
}


.modal-actions {
    display: flex;
    gap: 15px;
    margin-top: 25px;
    flex-wrap: wrap;
    justify-content: center;
}

.modal-action-btn {
    background: linear-gradient(135deg, #00808f 0%, #00add0 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(0, 128, 143, 0.2);
}

.modal-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 128, 143, 0.3);
    background: linear-gradient(135deg, #005a66 0%, #008ca6 100%);
}

.modal-action-btn.map-btn {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);
    position: relative;
    overflow: hidden;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.modal-action-btn.map-btn:hover {
    background: linear-gradient(135deg, #047857 0%, #059669 100%);
    box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
    transform: translateY(-3px);
}

.modal-action-btn.map-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modal-action-btn.map-btn:hover::before {
    left: 100%;
}

.modal-divider {
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 128, 143, 0.3) 50%, transparent 100%);
    margin: 20px 0;
}

.modal-badge {
    background: linear-gradient(135deg, #00808f 0%, #00add0 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 10px;
    box-shadow: 0 2px 8px rgba(0, 128, 143, 0.2);
}

.close {
    position: absolute;
    top: 18px;
    right: 24px;
    font-size: 1.5rem;
    color: #2563eb;
    cursor: pointer;
    font-weight: bold;
    background: none;
    border: none;
}

#modalImage {
    width: 200px;
    height: 200px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #00808f;
    margin-bottom: 20px;
    box-shadow: 0 4px 10px rgba(0, 128, 143, 0.2);
}

#modalName {
    font-size: 28px;
    color: #00808f;
    margin-bottom: 10px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

#modalPosition, #modalDepartment {
    font-size: 18px;
    margin: 15px auto;
    text-align: center;
    max-width: 400px;
    width: 100%;
    border: 1px solid #00add0;
    border-radius: 15px;
    background-color: #ffffff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    padding: 15px 20px;
    font-weight: 600;
    color: #00808f;
}

#modalOffice {
    margin: 15px 0;
    text-align: center;
    width: 100%;
    display: flex;
    justify-content: center;
}

#modalPosition:hover, #modalDepartment:hover {
    background-color: #e0f7fa;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}



#modalDescription {
    font-size: 16px;
    margin: 20px 0;
    text-align: center;
    max-width: 90%;
    border: 1px solid #00add0;
    border-radius: 10px;
    padding: 15px;
    background-color: #f0f8ff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    color: #333;
    line-height: 1.5;
}

.modal-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 1px solid #00add0;
    border-radius: 10px;
    background-color: #f0f8ff;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.modal-info p {
    font-size: 16px;
    margin: 10px 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-info .icon {
    margin-right: 10px;
    color: #00add0;
    font-size: 18px;
    font-size: 20px;
}

@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        padding: 20px;
    }
    #modalImage {
        width: 150px;
        height: 150px;
    }
    #modalName {
        font-size: 24px;
    }
    .modal-info p {
        font-size: 14px;
    }
}

@media screen and (max-width: 768px) {
    .navbar {
        flex-direction: column;
        align-items: center;
    }
    .header-title {
        position: static;
        transform: none;
        margin: 10px 0;
    }
    .search-container {
        position: static;
        transform: none;
        width: 100%;
        margin-top: 10px;
    }
}

.search-button {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}

#backToTopBtn {
    position: fixed;
    bottom: 32px;
    right: 32px;
    background: #2563eb;
    color: #fff;
    border: none;
    border-radius: 50px;
    padding: 10px 22px;
    font-size: 1.1rem;
    box-shadow: 0 2px 8px rgba(37,99,235,0.10);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1001;
    transition: background 0.2s;
}

#backToTopBtn:hover {
    background: #1741a6;
}

#backToTopBtn i {
    font-size: 28px;
    margin-bottom: 7px;
}

#backToTopBtn span {
    font-size: 14px;
    font-weight: bold;
}



@media (max-width: 768px) {
    #backToTopBtn {
        padding: 14px 9px;
        font-size: 15px;
        width: 90px;
        right: 32px;
    }
    #backToTopBtn i {
        font-size: 25px;
    }
    #backToTopBtn span {
        font-size: 13px;
    }
}

#Footer {
    background: #fff;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -2px 12px rgba(0,0,0,0.06);
    padding: 12px 32px;
    text-align: right;
    color: #666;
    font-size: 0.98rem;
    margin-top: 32px;
}

#Footer p {
    margin: 0;
}

body.dark-mode {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: #e2e8f0;
}

.dark-mode header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid #334155;
}

.dark-mode .navbar {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 0 0 16px 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark-mode .navbar a,
.dark-mode .navbar {
    color: #e2e8f0;
}

.dark-mode .header-title {
    color: #3b82f6;
}




.dark-mode .main-search-section {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.dark-mode .search-input-group {
    background: #334155;
    border-color: #475569;
}

.dark-mode .search-input-group:focus-within {
    border-color: #3b82f6;
    background: #475569;
}

.dark-mode .search-icon {
    color: #94a3b8;
}

.dark-mode .main-search-input {
    color: #e2e8f0;
}

.dark-mode .main-search-input::placeholder {
    color: #94a3b8;
}

.dark-mode .search-clear-btn {
    color: #94a3b8;
}

.dark-mode .search-clear-btn:hover {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.2);
}


.dark-mode .theme-toggle-btn {
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
    border-color: #64748b;
    color: #f59e0b;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark-mode .theme-toggle-btn:hover {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-color: #f59e0b;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4);
}


.dark-mode .employee_listing .employee {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
    border: 1px solid #64748b;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.4),
        0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark-mode .employee_listing .employee h2,
.dark-mode .employee_listing .employee .first-name {
    color: #3b82f6;
}

.dark-mode .employee_listing .employee .last-name {
    color: #60a5fa;
}

.dark-mode .employee_listing .employee .position {
    color: #94a3b8;
}

.dark-mode .employee_listing .employee .job_type {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border: 1px solid #475569;
    color: #e2e8f0;
}

.dark-mode .employee_listing .employee:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-color: #60a5fa;
    transform: scale(1.02) translateY(-2px);
    box-shadow:
        0 8px 32px rgba(59, 130, 246, 0.3),
        0 4px 16px rgba(0, 0, 0, 0.2);
}

.dark-mode .employee_listing .employee:hover h2,
.dark-mode .employee_listing .employee:hover .first-name,
.dark-mode .employee_listing .employee:hover .last-name,
.dark-mode .employee_listing .employee:hover .position {
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark-mode .employee_listing .employee:hover .job_type {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-color: #ffffff;
    color: #ffffff;
}


.dark-mode #Footer {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: #94a3b8;
    border-top: 1px solid #475569;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.dark-mode #Footer p {
    color: #e2e8f0;
}

.dark-mode #Footer span {
    color: #3b82f6;
    font-weight: 600;
}


.dark-mode .modal-content {
    background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    color: #e2e8f0;
    border: 2px solid #3b82f6;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.dark-mode .modal-content h2 {
    color: #3b82f6;
}

.dark-mode .modal-content .modal-info-group p {
    color: #94a3b8;
}

.dark-mode .modal-content .contact-item {
    color: #e2e8f0;
}

.dark-mode .modal-content .contact-item .icon {
    color: #3b82f6;
}

.dark-mode .modal-content .close {
    color: #94a3b8;
    background: rgba(148, 163, 184, 0.1);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.dark-mode .modal-content .close:hover {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

.dark-mode .close {
    color: #00add0;
}

.dark-mode .close:hover,
.dark-mode .close:focus {
    color: #008ca6;
}

.dark-mode #modalName {
    color: #00add0;
}

.dark-mode #modalPosition,
.dark-mode #modalDepartment {
    background-color: #333;
    color: #f0f0f0;
    border-color: #00add0;
    margin: 15px auto;
    max-width: 400px;
    width: 100%;
}

.dark-mode #modalPosition:hover,
.dark-mode #modalDepartment:hover {
    background-color: #444;
}

.dark-mode #modalDescription {
    background-color: #333;
    color: #f0f0f0;
    border-color: #00add0;
}

.dark-mode .modal-info {
    background-color: #333;
    border-color: #00add0;
}

.dark-mode .modal-info p {
    color: #f0f0f0;
}

.dark-mode .modal-info .icon {
    color: #00add0;
}

.dark-mode .contact-item {
    background-color: #2a2a2a;
    border-left-color: #00add0;
}

.dark-mode .contact-item:hover {
    background-color: #333;
}

.dark-mode .contact-item span {
    color: #f0f0f0;
}

.dark-mode .modal-header {
    background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
    border-color: #00add0;
}

.dark-mode .modal-info-group p {
    background-color: #333;
    border-color: #00add0;
    color: #f0f0f0;
}

.dark-mode .modal-action-btn.map-btn {
    background: linear-gradient(135deg, #047857 0%, #059669 100%);
    box-shadow: 0 4px 15px rgba(4, 120, 87, 0.3);
}

.dark-mode .modal-action-btn.map-btn:hover {
    background: linear-gradient(135deg, #065f46 0%, #047857 100%);
    box-shadow: 0 8px 25px rgba(4, 120, 87, 0.4);
}


.header-actions {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle-btn {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #64748b;
    font-size: 18px;
}

.theme-toggle-btn:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}




.dark-mode #backToTopBtn {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: #ffffff;
    border: 1px solid #60a5fa;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.dark-mode #backToTopBtn:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
    background-color: #ccc;
    border-radius: 34px;
    border: 2px solid #666;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.toggle-switch::before {
    content: '';
    position: absolute;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    transition: 0.4s;
}

.toggle-switch.active {
    background-color: #2196F3;
}

.toggle-switch.active::before {
    transform: translateX(26px);
}

.toggle-switch::after,
.toggle-switch.active::after {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.icon {
    width: 24px;
    height: 24px;
    vertical-align: middle;
    margin-right: 8px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.modal-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    padding: 1px;
    border: 1px solid #00add0;
    border-radius: 15px;
    background: linear-gradient(135deg, #f0f8ff, #e0f7fa);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    flex-wrap: wrap;
}

.modal-info-group {
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 5px;
    flex-wrap: wrap;
}

.modal-info-group p {
    flex: 1 1 50%;
    max-width: 300px;
    text-align: center;
    margin: 0;
    padding: 1px;
    border: 1px solid #00add0;
    border-radius: 1px;
    background-color: #ffffff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
}

.modal-info-group p span {
    display: block;
    font-weight: bold;
    margin-bottom: 3px;
}

.modal-header:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}


.employee_directory > .map-container, .employee_directory > img, .employee_directory > .office-map {
    flex: 1 1 0%;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    margin-left: 0;
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 0;
}
.employee_directory > img {
    max-width: 100%;
    height: auto;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
}